<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <table class="table table-hover" id="myTable">
                    <thead class="text-center">
                        <tr>
                            <th width="5%">ID</th>
                            <th width="30%"><PERSON><PERSON></th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th><PERSON>ambar <PERSON></th>
                            <th>Gambar <PERSON></th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div id="imagePopup" class="image-popup">
    <span class="close-popup">&times;</span>
    <div class="popup-content">
        <img id="popupImage" class="popup-img" src="" alt="Gambar">
    </div>
    <button class="reset-zoom-btn" onclick="resetZoom()">Reset Zoom</button>
</div>

<style>
    .image-popup {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        z-index: 1000;
    }
    .popup-content {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: auto;
    }
    .popup-img {
        max-height: 90vh;
        max-width: 90vw;
        min-height: 50vh;
        min-width: auto;
        object-fit: contain;
        transform-origin: center;
        transition: transform 0.1s ease;
        cursor: zoom-in;
    }

    .close-popup {
        position: absolute;
        top: 15px;
        right: 25px;
        color: #f1f1f1;
        font-size: 40px;
        font-weight: bold;
        cursor: pointer;
    }

    .reset-zoom-btn {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        padding: 8px 16px;
        background: #fff;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .img-thumbnail.preview-image {
        cursor: pointer !important;
    }
</style>

<script>
    $(document).ready(function() {
        $('#myTable').DataTable({
            "processing": true,
            "serverSide": true,
            "ajax": {
                "url": "<?php echo site_url('dashboard/get_data_camera_alter'); ?>",
                "type": "POST",
                "data": function(d) {
                    d.channel_id = <?php echo $channel_id; ?>;
                }
            },
            "order": [[3, "desc"]], 
            "columns": [{
                    "data": "record_id"
                },
                {
                    "data": "person_name"
                },
                {
                    "data": "channel_name"
                },
                {
                    "data": "passing_datetime"
                },
                {
                    "data": "similarity",
                    "render": function(data, type, row) {
                        return data + " %";
                    }
                },
                {
                    "data": "image_small",
                    "render": function(data, type, row) {
                        var fullUrl = 'http://192.168.7.60/cron/cctv/' + data;
                        return '<img src="' + fullUrl + '" width="50" height="50" class="img-thumbnail preview-image" data-url="' + fullUrl + '">';
                    }
                },
                {
                    "data": "image_big",
                    "render": function(data, type, row) {
                        var fullUrl = 'http://192.168.7.60/cron/cctv/' + data;
                        return '<img src="' + fullUrl + '" width="100" height="100" class="img-thumbnail preview-image" data-url="' + fullUrl + '">';
                    }
                }
            ]
        });
    let scale = 1;
    const ZOOM_SPEED = 0.1;
    const MAX_ZOOM = 4;
    const MIN_ZOOM = 0.5;
    
    // Event listener untuk menampilkan gambar
    $(document).on('click', '.preview-image', function() {
        var imgUrl = $(this).data('url');
        $('#popupImage').attr('src', imgUrl);
        $('#imagePopup').fadeIn();
        scale = 1;
        $('#popupImage').css('transform', `scale(${scale})`);
    });
    
    // Close popup
    $('.close-popup').click(function() {
        $('#imagePopup').fadeOut();
    });
    
    // Close with Escape key
    $(document).keydown(function(e) {
        if (e.key === "Escape") {
            $('#imagePopup').fadeOut();
        }
    });
    // Zoom functionality
    $('.popup-content').on('wheel', function(e) {
        e.preventDefault();
        const delta = e.originalEvent.deltaY;
        const img = $('#popupImage')[0];
        const rect = img.getBoundingClientRect();
        const offsetX = e.clientX - rect.left;
        const offsetY = e.clientY - rect.top;
        
        if (delta < 0) {
            scale = Math.min(scale + ZOOM_SPEED, MAX_ZOOM);
        } else {
            scale = Math.max(scale - ZOOM_SPEED, MIN_ZOOM);
        }
        
        $('#popupImage').css({
            'transform': `scale(${scale})`,
            'transform-origin': `${(offsetX / rect.width) * 100}% ${(offsetY / rect.height) * 100}%`
        });
    });
    // Double click zoom
    $('#popupImage').on('dblclick', function(e) {
        const img = this;
        const rect = img.getBoundingClientRect();
        const offsetX = e.clientX - rect.left;
        const offsetY = e.clientY - rect.top;
        
        if (scale < MAX_ZOOM) {
            scale = Math.min(scale + 1, MAX_ZOOM);
        } else {
            scale = MIN_ZOOM;
        }
        
        // Update cursor based on scale
        $(this).css({
            'transform': `scale(${scale})`,
            'transform-origin': `${(offsetX / rect.width) * 100}% ${(offsetY / rect.height) * 100}%`,
            'cursor': scale >= MAX_ZOOM ? 'zoom-out' : 'zoom-in'
        });
    });
    // Update cursor on wheel zoom
    $('.popup-content').on('wheel', function(e) {
        e.preventDefault();
        const delta = e.originalEvent.deltaY;
        const img = $('#popupImage')[0];
        const rect = img.getBoundingClientRect();
        const offsetX = e.clientX - rect.left;
        const offsetY = e.clientY - rect.top;
        
        if (delta < 0) {
            scale = Math.min(scale + ZOOM_SPEED, MAX_ZOOM);
        } else {
            scale = Math.max(scale - ZOOM_SPEED, MIN_ZOOM);
        }
        
        // Update cursor based on scale
        $(img).css({
            'transform': `scale(${scale})`,
            'transform-origin': `${(offsetX / rect.width) * 100}% ${(offsetY / rect.height) * 100}%`,
            'cursor': scale >= MAX_ZOOM ? 'zoom-out' : 'zoom-in'
        });
    });
    });
    
    function resetZoom() {
        scale = 1;
        $('#popupImage').css({
            'transform': `scale(${scale})`,
            'transform-origin': 'center center'
        });
    }
</script>