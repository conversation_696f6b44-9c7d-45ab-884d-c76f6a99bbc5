<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Tambah extends CI_Controller {

	function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') != TRUE){
      redirect('start');
    }
    date_default_timezone_set("Asia/Bangkok");
    // $this->load->model(array('Dashboard_model'));
  }

	public function index()
	{
		$data = array(
			'title'				=> 'Tambah', 
			'header'			=> 'Tambah', 
			'button'			=> '', 
			'isi' 				=> 'v_tambah',
			);

		$this->load->view('layout/wrapper', $data);
	}

	public function get_data_jadwal(){
        $draw   = intval($this->input->POST("draw"));
        $start  = intval($this->input->POST("start"));
        $length = intval($this->input->POST("length"));
    
        $listJadwal = $this->Dashboard_model->list_jadwal1();
    
        $data = array();
        $no = 1;
        foreach ($listJadwal->result() as $LJ) {
        

        if($LJ->STATUS == 1){
            $button = '<a type="button" id="selesai" class="btn btn-success selesai" data="'.$LJ->ID.'">Selesai</a>
                        <a type="button" id="hapus" class="btn btn-danger hapus" data="'.$LJ->ID.'">Hapus</a>';
        }else{
            $button = '<a type="button" id="praktek" class="btn btn-primary praktek" data="'.$LJ->ID.'">Praktek</a>
                        <a type="button" id="hapus" class="btn btn-danger hapus" data="'.$LJ->ID.'">Hapus</a>';
        } 

        $data[] = array(
            $no,
            $LJ->NAMA_LENGKAP,
            $LJ->RUANGAN,
            $LJ->NOCOUNTER,
            $LJ->RUANG_ASAL,
            $LJ->JADSTATUS,
            $button
        );
        $no++;
        }
    
        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listJadwal->num_rows(),
            "recordsFiltered" => $listJadwal->num_rows(),
            "data"            => $data
        );
        echo json_encode($output);
    }

    public function simpanJadwal()
    {
        $this->db->trans_begin();

        $post = $this->input->post();

        $dataJadwal = array (
        'NAMA'     			  => $post['DOKTER'],
        'RUANGAN'     		=> $post['RUANGAN'],
        'NOCOUNTER'     	=> $post['COUNTER'],
        'RUANG_ASAL'     	=> $post['RUANG_ASAL'],
        );
        // echo "<pre>";print_r($dataJadwal);echo "</pre>";

        $this->Dashboard_model->simpan_jadwal($dataJadwal);

        if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
        //   $this->session->set_flashdata('error', "Gagal Simpan");
        } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
        //   $this->session->set_flashdata('success', "Berhasil Simpan"); 
        }

        echo json_encode($result);
    }

    public function selesai()
    {
        $this->db->trans_begin();

        $post = $this->input->post();
        $id = $post['ID'];

        $dataEdit = array (
            'STATUS'     				=> 2
        );
        // echo "<pre>";print_r($dataEdit);echo "</pre>";

        $this->Dashboard_model->update($id,$dataEdit);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

    	echo json_encode($result);
    }

    public function praktek()
    {
        $this->db->trans_begin();

        $post = $this->input->post();
        $id = $post['ID'];

        $dataEdit = array (
            'STATUS'     				=> 1
        );
        // echo "<pre>";print_r($dataEdit);echo "</pre>";

        $this->Dashboard_model->update($id,$dataEdit);

        if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
        } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function hapus()
    {
        $this->db->trans_begin();

        $post = $this->input->post();
        $id = $post['ID'];

        $dataEdit = array (
            'STATUS'     				=> 0
        );
        // echo "<pre>";print_r($dataEdit);echo "</pre>";

        $this->Dashboard_model->update($id,$dataEdit);

        if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
        } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function getDokter()
    {
      $result = $this->Dashboard_model->list_dokter();
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['DOKTER'];
          $sub_array['text'] = $row['NAMA_LENGKAP'];
          $data[] = $sub_array;
      }
      echo json_encode($data);
    }

    function getRuangan()
  	{
      $result = $this->Dashboard_model->listRuangan();
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['ID'];
          $sub_array['text'] = $row['DESKRIPSI'];
          $data[] = $sub_array;
      }
      // $output = array(
      //     "item" -> $data
      // );
      echo json_encode($data);
  	}

}
