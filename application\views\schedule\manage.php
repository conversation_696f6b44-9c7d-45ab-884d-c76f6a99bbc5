<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h3>List Display Jadwal Dokter</h3>
                <form id="filterDate">
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label for="startDate">Start Date</label>
                                <input type="text" class="form-control datepicker" value="<?= $start_date ?>" id="startDate" placeholder="Pick a date">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label for="endDate">End Date</label>
                                <input type="text" class="form-control datepicker" value="<?= $end_date ?>" id="endDate" placeholder="Pick a date">
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12 text-end">
                            <button type="button" class="btn btn-primary" id="searchButton">Search</button>
                        </div>
                    </div>
                </form>
                <div class="table table-responsive mt-5">
                    <table class="table table-hover" id="listScheduleDoctor">
                        <thead>
                            <tr class="text-center">
                                <th width="10%">No.</th>
                                <th>Nama Dokter</th>
                                <th>Kamar</th>
                                <th>Jadwal</th>
                                <th>Ruangan</th>
                                <th>Keterangan</th>
                                <th>Note</th>
                                <th>Note Display</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">Edit Jadwal Dokter</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <div class="mb-3">
                        <label for="roomNumber" class="form-label">Nomor Kamar</label>
                        <input type="text" class="form-control" id="roomNumber" name="room_number" required>
                    </div>
                    <div class="mb-3">
                        <label for="status" class="form-label">Keterangan</label>
                        <select class="form-select" id="status" name="status" required>
                            <?php foreach ($status_references as $status): ?>
                                <option value="<?= $status['ID']; ?>"><?= $status['DESKRIPSI']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="catatan" class="form-label">Catatan</label>
                        <textarea class="form-control" id="catatan" name="catatan" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="catatan_display" class="form-label" id="catatan_display_label">
                            Catatan Display <sup style="color: red;">*akan muncul di display dokter*</sup>
                        </label>
                        <textarea class="form-control" id="catatan_display" name="catatan_display" rows="3" placeholder="Masukkan catatan di sini"></textarea>
                    </div>
                    <?php if ($this->session->userdata('id') == 413) { ?>
                        <div class="mb-3">
                            <label for="jam_awal" class="form-label">Jam Awal</label>
                            <input type="text" class="form-control" id="jam_awal" name="jam_awal" required>
                        </div>
                        <div class="mb-3">
                            <label for="jam_akhir" class="form-label">Jam Akhir</label>
                            <input type="text" class="form-control" id="jam_akhir" name="jam_akhir" required>
                        </div>
                    <?php } ?>
                    <input type="hidden" id="jadwal_id" name="jadwal_id">
                    <input type="hidden" id="ruangan_id" name="ruangan_id">
                    <input type="hidden" id="tanggal" name="tanggal">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveChanges">Save changes</button>
            </div>
        </div>
    </div>
</div>



<script>
    $(document).ready(function() {
        $('#status').change(function() {
            const selectedValue = $(this).val();
            const $label = $('#catatan_display_label');
            const $textarea = $('#catatan_display');

            if (selectedValue === '4') {
                $label.html('Estimasi Keterlambatan <sup style="color: red;">*akan muncul di display dokter*</sup>');
                $textarea.attr('placeholder', 'Masukkan estimasi keterlambatan di sini');
            } else {
                $label.html('Catatan Display <sup style="color: red;">*akan muncul di display dokter*</sup>');
                $textarea.attr('placeholder', 'Masukkan catatan di sini');
            }
        });

        $('.datepicker').bootstrapMaterialDatePicker({
            weekStart: 0,
            time: false,
            clearButton: true // Add this line to show the clear button
        });

        function searchFunction() {
            var startDate = $('#startDate').val();
            var endDate = $('#endDate').val();

            var currentUrl = window.location.pathname; // Get current path
            var url = currentUrl; // Construct URL with current path
            var queryParams = '';

            if (startDate) {
                queryParams += 'start_date=' + encodeURIComponent(startDate);
            }
            if (endDate) {
                if (queryParams.length > 0) {
                    queryParams += '&';
                }
                queryParams += 'end_date=' + encodeURIComponent(endDate);
            }

            if (queryParams.length > 0) {
                url += '?' + queryParams;
            }

            window.location.href = url;
        }

        $('#searchButton').click(function(e) {
            e.preventDefault(); // Prevent the default link behavior
            // searchFunction();
            Swal.fire({
                title: 'Mohon Tunggu...',
                text: 'Sedang mengambil data...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            $('#listScheduleDoctor').DataTable().ajax.reload(null, false);

        });
        $('.datepicker').on('change', function() {
            // searchFunction();
            Swal.fire({
                title: 'Mohon Tunggu...',
                text: 'Sedang mengambil data...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            $('#listScheduleDoctor').DataTable().ajax.reload(null, false);
        });

        $('#listScheduleDoctor').DataTable({
            "processing": true,
            "serverSide": true,
            "ajax": {
                "url": "<?= base_url('schedule/get_schedule_data_ajax') ?>",
                "type": "POST",
                "data": function(d) {
                    d.start_date = $('#startDate').val();
                    d.end_date = $('#endDate').val();
                    d.gedung = '<?= $subpage ?>'; // Assuming $subpage contains gedung value
                }
            },
            "columns": [{
                    "data": null,
                    "orderable": false,
                    "searchable": false,
                    "render": function(data, type, row, meta) {
                        return meta.row + meta.settings._iDisplayStart + 1;
                    }
                },
                {
                    "data": "DOCTOR_NAME"
                },
                {
                    "data": "KAMAR",
                    "render": function(data, type, row) {
                        return data ? data : '-';
                    }
                },
                {
                    "data": null,
                    "render": function(data, type, row) {
                        return `<div>(${row.TANGGAL})</div>
                        <div>${row.AWAL}.00 - ${row.AKHIR}.00</div>`;
                    }
                },
                {
                    "data": "DESKRIPSI"
                },
                {
                    "data": "DOCTOR_STATUS",
                    "render": function(data, type, row) {
                        let statusColor = 'dark'; // Default color
                        let statusText = row.STATUS_TEXT || 'Terjadwal'; // Use STATUS_TEXT from the row

                        // Logic for badge color based on DOCTOR_STATUS
                        switch (data) {
                            case '1':
                                statusColor = 'dark';
                                break;
                            case '6':
                                statusColor = 'success';
                                break;
                            case '2':
                                statusColor = 'warning';
                                break;
                            case '3':
                                statusColor = 'info';
                                break;
                            case '4':
                                statusColor = 'danger';
                                break;
                            case '5':
                                statusColor = 'secondary';
                                break;
                        }

                        return `<span data-id="${data}" class="badge bg-${statusColor}">${statusText}</span>`;
                    }
                },
                {
                    "data": "NOTE"
                },
                {
                    "data": "NOTE_DISPLAY"
                },
                {
                    "data": "JADWAL_ID",
                    "orderable": false,
                    "searchable": false,
                    "render": function(data, type, row) {
                        return `<button data-id="${data}" class="btn btn-primary btn-sm btn-edit">
                            <i class="fas fa-pencil-alt"></i> Edit
                        </button>`;
                    }
                }
            ],
            "order": [
                [1, 'asc']
            ], // Order by tanggal column
            "language": {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            }
        }).on('xhr.dt', function() {
            Swal.close(); // Tutup SweetAlert setelah data selesai dimuat
        });;

        // Handle the click event of the edit button
        // Ganti event handler btn-edit
        $(document).on('click', '.btn-edit', function() {
            var jadwal_id = $(this).data('id');

            // Gunakan AJAX untuk mengambil data
            $.ajax({
                url: '<?= base_url('schedule/get_schedule_detail') ?>',
                method: 'GET',
                data: {
                    jadwal_id: jadwal_id
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        var data = response.data;

                        // Set values in modal
                        $('#jadwal_id').val(jadwal_id);
                        $('#roomNumber').val(data.KAMAR || '');
                        $('#status').val(data.STATUS);
                        $('#catatan').val(data.NOTE || '');
                        $('#catatan_display').val(data.NOTE_DISPLAY || '');
                        $('#jam_awal').val(data.AWAL || '');
                        $('#jam_akhir').val(data.AKHIR || '');
                        $('#ruangan_id').val(data.RUANGAN || '');
                        $('#tanggal').val(data.TANGGAL || '');

                        // Trigger status change to update label if needed
                        $('#status').trigger('change');

                        // Show the modal
                        // Show the modal
                        var myModal = new bootstrap.Modal(document.getElementById('editModal'));
                        myModal.show();

                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal!',
                            text: response.message || 'Gagal mengambil data.'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Terjadi Kesalahan!',
                        text: 'Tidak dapat menghubungi server, silakan coba lagi.',
                    });
                }
            });
        });

        $('#saveChanges').click(function() {
            $.ajax({
                url: '<?= base_url('/schedule/update_schedule') ?>', // Update the endpoint to match your controller method
                method: 'POST',
                data: $('#editForm').serialize(), // Serialize form data
                success: function(response) {
                    var result = JSON.parse(response);
                    if (result.status === 'success') {
                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil!',
                            text: 'Data berhasil diperbarui.',
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            $('#listScheduleDoctor').DataTable().ajax.reload(null, false); // Refresh DataTable
                        });

                        // Show the modal
                        var myModal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
                        myModal.hide();
                        // Hide modal
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal!',
                            text: result.message,
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error(xhr.responseText); // Log error details
                    Swal.fire({
                        icon: 'error',
                        title: 'Terjadi Kesalahan!',
                        text: 'Tidak dapat menghubungi server, silakan coba lagi.',
                    });
                }
            });
            var ruangan_id = $('#ruangan_id').val();
            var tanggal = $('#tanggal').val();
            var today = new Date().toISOString().split('T')[0];
            // Jika tanggal sesuai dengan hari ini, jalankan AJAX request
            if (tanggal === today) {
                $.ajax({
                    type: 'POST',
                    url: 'http://192.168.7.158:8086/updatedisplaydokter',
                    dataType: 'JSON',
                    data: {
                        idruangan: ruangan_id
                    },
                    success: function(data) {
                        console.log('Update display dokter berhasil');
                    },
                    error: function(xhr, status, error) {
                        console.error('Error update display dokter:', xhr.responseText);
                    }
                });
            }
        });
    });
</script>