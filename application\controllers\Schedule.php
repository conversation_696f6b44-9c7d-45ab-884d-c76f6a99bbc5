<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Schedule extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') != TRUE) {
            redirect('start');
        }
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('Schedule_model'));
    }

    public function index()
    {

        $data['title'] = 'Jadwal Dokter';
        $data['page'] = 'schedule';
        $data['button'] = '';
        $data['isi'] = 'schedule/index';
        $this->load->view('layout/wrapper', $data);
    }

    public function manage($gedung = '')
    {
        if (!$gedung) {
            redirect('schedule');
        }
        
        $start_date = date('Y-m-d');
        $end_date = date('Y-m-d');
        $data['title'] = 'Jadwal Dokter';
        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;
        $data['header'] = '';
        $data['page'] = 'schedule';
        $data['button'] = '';
        $data['subpage'] = $gedung;
        $data['status_references'] = $this->Schedule_model->get_status_references();
        $data['isi'] = 'schedule/manage';

        $this->load->view('layout/wrapper', $data);
    }

    public function update_schedule()
    {
        $this->load->library('form_validation');
        $this->form_validation->set_rules('jadwal_id', 'Jadwal ID', 'required');
        // Tambahkan validasi lainnya jika diperlukan

        if ($this->form_validation->run() == FALSE) {
            echo json_encode(['status' => 'error', 'message' => validation_errors()]);
            return;
        }

        $jadwal_id = $this->input->post('jadwal_id');
        $roomNumber = $this->input->post('room_number');
        $status = $this->input->post('status');
        $note = $this->input->post('catatan');
        $note_display = $this->input->post('catatan_display');
        $jam_awal = $this->input->post('jam_awal') ?? null;
        $jam_akhir = $this->input->post('jam_akhir') ?? null;


        $data = [
            'ID_JADWAL'     => $jadwal_id,
            'KAMAR'         => $roomNumber,
            'STATUS'        => $status,
            'NOTE'          => $note,
            'NOTE_DISPLAY'  => $note_display,
        ];

        // Mulai transaksi
        $this->db->trans_start();

        try {

            if (!empty($jam_awal) && !empty($jam_akhir)) {
                $data_rmj = [
                    'AWAL'          => $jam_awal,
                    'AKHIR'         => $jam_akhir,
                    'TANGGAL_UBAH'  => date('Y-m-d H:i:s'),
                    'OLEH'          => $this->session->userdata('id')
                ];

                $this->Schedule_model->update_schedule($jadwal_id, $data_rmj);
            }
            // Simpan atau update jadwal display
            $this->Schedule_model->save_or_update_schedule_display($data);

            // Jika tidak ada error, commit transaksi
            $this->db->trans_complete();

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Transaksi gagal, perubahan tidak disimpan.');
            }

            echo json_encode(['status' => 'success', 'message' => 'Data updated successfully']);
        } catch (Exception $e) {
            $this->db->trans_rollback(); // Rollback jika terjadi error
            echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }


    public function get_schedule_data()
    {
        $doctorId = $this->input->get('doctor_id');

        // Fetch data from the model
        $data = $this->Schedule_model->get_schedule_by_id($doctorId);

        if ($data) {
            echo json_encode(['status' => 'success', 'data' => $data]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Data tidak ditemukan.']);
        }
    }

    public function get_schedule_data_ajax()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $search = $this->input->post('search')['value'];
        $order = $this->input->post('order')[0];
        $start_date = $this->input->post('start_date');
        $end_date = $this->input->post('end_date');
        $gedung = $this->input->post('gedung');

        // Get total count
        $total = $this->Schedule_model->get_total_schedules($start_date, $end_date, $gedung);

        // Get filtered count
        $filtered = $this->Schedule_model->get_filtered_schedules($search, $start_date, $end_date, $gedung);

        // Get the data
        $data = $this->Schedule_model->get_schedule_data(
            $start,
            $length,
            $search,
            $order,
            $start_date,
            $end_date,
            $gedung
        );

        $response = [
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => $total,
            "recordsFiltered" => $filtered,
            "data" => $data
        ];

        echo json_encode($response);
    }

    public function get_schedule_detail()
    {
        $jadwal_id = $this->input->get('jadwal_id');

        if (!$jadwal_id) {
            echo json_encode([
                'status' => 'error',
                'message' => 'ID Jadwal tidak valid.'
            ]);
            return;
        }

        try {
            // Gunakan model untuk mengambil detail
            $detail = $this->Schedule_model->get_schedule_detail_by_jadwal_id($jadwal_id);

            if ($detail) {
                echo json_encode([
                    'status' => 'success',
                    'data' => $detail
                ]);
            } else {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Data tidak ditemukan.'
                ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'status' => 'error',
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ]);
        }
    }
}
