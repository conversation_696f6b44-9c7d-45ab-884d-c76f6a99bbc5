<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Schedule_model extends CI_Model
{

    public function __construct()
    {
        parent::__construct();
    }

    public function get_list_schedule_doctor($start_date = null, $end_date = null, $gedung = '')
    {
        $this->db->select('master.getNamaLengkapPegawai(mp.NIP) as DOCTOR_NAME, md.ID as DOCTOR_ID, dd.KAMAR, rmj.ID as JADWAL_ID, rmj.TANGGAL, rmj.AWAL, rmj.AKHIR, mr.DESKRIPSI, rmj.KUOTA, rmj.RUANGAN, dd.STATUS as DOCTOR_STATUS, dd.NOTE, dd.NOTE_DISPLAY');
        $this->db->from('remun_medis.jadwal rmj');
        $this->db->join('master.ruangan mr', 'mr.ID = rmj.RUANGAN', 'left');
        $this->db->join('master.dokter md', 'md.ID = rmj.DOKTER', 'left');
        $this->db->join('master.pegawai mp', 'md.NIP = mp.NIP', 'left');
        $this->db->join('db_layanan.display_dokter dd', 'dd.ID_JADWAL = rmj.ID', 'left');
        $this->db->join('master.referensi smf ', 'smf.ID = mp.SMF AND smf.JENIS = 26 AND smf.STATUS=1', 'left');
        $this->db->where('mr.JENIS', 5);
        if ($gedung == 'gedung-woman-and-child') {
            $this->db->where('mr.JENIS_KUNJUNGAN', 15);
        }
        if ($gedung == 'gedung-utama') {
            $this->db->where_in('mr.JENIS_KUNJUNGAN', array(1, 13, 14));
            $this->db->where_not_in('mr.ID', array(105020101, 105020102));
        }
        $this->db->where('rmj.STATUS', 1);

        // Apply date filtering if dates are provided
        if ($start_date) {
            $this->db->where('rmj.TANGGAL >=', $start_date);
        }
        if ($end_date) {
            $this->db->where('rmj.TANGGAL <=', $end_date);
        }
        $this->db->order_by("CASE 
                                WHEN rmj.RUANGAN = '105020704' THEN 1
                                WHEN rmj.RUANGAN = '105020705' THEN 2
                                WHEN rmj.RUANGAN = '105020708' THEN 3
                                WHEN rmj.RUANGAN = '105020706' THEN 4
                                WHEN rmj.RUANGAN = '105020201' THEN 5
                                WHEN rmj.RUANGAN = '105060101' THEN 6
                                ELSE 7
                            END, rmj.RUANGAN
                            , mr.DESKRIPSI ASC
                            , rmj.AWAL ASC
                            , rmj.AKHIR ASC
                            , smf.DESKRIPSI
                            , mp.NAMA asc");
        $query = $this->db->get();

        // Return result as an associative array
        return $query->result_array();
    }
    public function get_status_references()
    {
        return $this->db->where('jenis', 129)
            ->where('status', 1)
            ->get('master.referensi')
            ->result_array();
    }


    public function get_schedule_by_id($doctorId)
    {
        $this->db->select('KAMAR, STATUS');
        $this->db->where('ID_JADWAL', $doctorId);
        $query = $this->db->get('db_layanan.display_dokter');

        if ($query->num_rows() > 0) {
            return $query->row_array();
        } else {
            return false;
        }
    }

    public function get_total_schedules($start_date, $end_date, $gedung)
    {
        $this->_get_schedule_query($start_date, $end_date, $gedung);
        return $this->db->count_all_results();
    }

    public function get_filtered_schedules($search, $start_date, $end_date, $gedung)
    {
        $this->_get_schedule_query($start_date, $end_date, $gedung, $search);
        return $this->db->get()->num_rows();
    }

    public function get_schedule_data($start, $length, $search, $order, $start_date, $end_date, $gedung)
    {
        $this->_get_schedule_query($start_date, $end_date, $gedung, $search);

        // Add ordering
        $columns = array(
            0 => 'mp.NAMA',
            1 => 'mp.NAMA',
            2 => 'dd.KAMAR',
            3 => 'rmj.TANGGAL',
            4 => 'mr.DESKRIPSI',
            5 => 'dd.STATUS',
            6 => 'dd.NOTE',
            7 => 'dd.NOTE_DISPLAY',
            8 => 'rmj.ID'
        );

        if (isset($order)) {
            $this->db->order_by($columns[$order['column']], $order['dir']);
        }

        // Add limit
        if ($length != -1) {
            $this->db->limit($length, $start);
        }

        return $this->db->get()->result_array();
    }

    private function _get_schedule_query($start_date, $end_date, $gedung, $search = '')
    {
        // Base query - sama seperti get_list_schedule_doctor tapi dengan modifikasi
        $this->db->select('master.getNamaLengkapPegawai(mp.NIP) as DOCTOR_NAME, md.ID as DOCTOR_ID, 
                           dd.KAMAR, rmj.ID as JADWAL_ID, rmj.TANGGAL, rmj.AWAL, rmj.AKHIR, 
                           mr.DESKRIPSI, rmj.KUOTA, rmj.RUANGAN, dd.STATUS as DOCTOR_STATUS, mref.DESKRIPSI as STATUS_TEXT, 
                           dd.NOTE, dd.NOTE_DISPLAY');
        $this->db->from('remun_medis.jadwal rmj');
        $this->db->join('master.ruangan mr', 'mr.ID = rmj.RUANGAN', 'left');
        $this->db->join('master.dokter md', 'md.ID = rmj.DOKTER', 'left');
        $this->db->join('master.pegawai mp', 'md.NIP = mp.NIP', 'left');
        $this->db->join('db_layanan.display_dokter dd', 'dd.ID_JADWAL = rmj.ID', 'left');
        $this->db->join('master.referensi smf', 'smf.ID = mp.SMF AND smf.JENIS = 26 AND smf.STATUS=1', 'left');
        $this->db->join('master.referensi mref', 'mref.ID = dd.STATUS AND mref.JENIS = 129 AND mref.STATUS = 1', 'left');
        // Add your existing conditions
        $this->db->where('mr.JENIS', 5);
        if ($gedung == 'gedung-woman-and-child') {
            $this->db->where('mr.JENIS_KUNJUNGAN', 15);
        }
        if ($gedung == 'gedung-utama') {
            $this->db->where_in('mr.JENIS_KUNJUNGAN', array(1, 13, 14));
            $this->db->where_not_in('mr.ID', array(105020101, 105020102));
        }
        $this->db->where('rmj.STATUS', 1);

        // Add date filters
        if ($start_date) {
            $this->db->where('rmj.TANGGAL >=', $start_date);
        }
        if ($end_date) {
            $this->db->where('rmj.TANGGAL <=', $end_date);
        }

        // Add search condition
        if ($search) {
            $this->db->group_start();
            $this->db->like('master.getNamaLengkapPegawai(mp.NIP)', $search);
            $this->db->or_like('dd.KAMAR', $search);
            $this->db->or_like('mr.DESKRIPSI', $search);
            $this->db->or_like('dd.NOTE', $search);
            $this->db->or_like('dd.NOTE_DISPLAY', $search);
            $this->db->or_like('mref.DESKRIPSI', $search);
            $this->db->group_end();
        }
    }

    public function get_schedule_detail_by_jadwal_id($jadwal_id)
    {
        // Query pertama
        $this->db->select('dd.KAMAR, dd.STATUS, dd.NOTE, dd.NOTE_DISPLAY, rmj.AWAL, rmj.AKHIR, rmj.RUANGAN, rmj.TANGGAL');
        $this->db->from('remun_medis.jadwal rmj');
        $this->db->join('db_layanan.display_dokter dd', 'dd.ID_JADWAL = rmj.ID', 'left');
        $this->db->where('rmj.ID', $jadwal_id);

        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            return $query->row_array();
        }

        // Jika tidak ada di display_dokter, cari informasi default
        $this->db->select('NULL AS KAMAR, NULL AS STATUS, NULL AS NOTE, NULL AS NOTE_DISPLAY', false); // False mencegah escape
        $this->db->from('remun_medis.jadwal');
        $this->db->where('ID', $jadwal_id);

        $default_query = $this->db->get();

        return $default_query->num_rows() > 0 ? $default_query->row_array() : false;
    }

    public function save_or_update_schedule_display($data)
    {
        try {
            $this->db->where('ID_JADWAL', $data['ID_JADWAL']);
            $query = $this->db->get('db_layanan.display_dokter');

            if ($query->num_rows() > 0) {
                // Record exists, perform an update
                $data['UPDATED_AT'] = date('Y-m-d H:i:s');
                $data['UPDATED_BY'] = $this->session->userdata('id');
                $this->db->where('ID_JADWAL', $data['ID_JADWAL']);
                if (!$this->db->update('db_layanan.display_dokter', $data)) {
                    throw new Exception('Gagal memperbarui data jadwal display.');
                }
            } else {
                // Record does not exist, perform an insert
                $data['CREATED_AT'] = date('Y-m-d H:i:s');
                $data['CREATED_by'] = $this->session->userdata('id');
                if (!$this->db->insert('db_layanan.display_dokter', $data)) {
                    throw new Exception('Gagal menyimpan data jadwal display.');
                }
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    public function update_schedule($id_jadwal = '', $data = [])
    {
        try {
            $this->db->where('ID', $id_jadwal);
            if (!$this->db->update('remun_medis.jadwal', $data)) {
                throw new Exception('Gagal memperbarui data jadwal.');
            }
        } catch (Exception $e) {
            throw $e;
        }
    }
}
