<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Dashboard extends CI_Controller
{

    function __construct()
    {
        parent::__construct();
        date_default_timezone_set("Asia/Jakarta");
        $this->load->model('Cctv_model');
        $this->load->model('CctvAlterModel');
    }

    public function camera($noCamera)
    {
        $data = array(
            'title'         => 'HOME',
            'header'        => 'HOME',
            'page'          => 'camera',
            'subpage'       => "camera-$noCamera",
            'channel_id'    => $noCamera,
            'button'        => '',
            'isi'           => 'v_data_camera',
        );

        $this->load->view('layout/wrapper', $data);
    }

    public function camera_alter($noCamera)
    {
        $data = array(
            'title'         => 'HOME',
            'header'        => 'HOME',
            'page'          => 'camera_alter',
            'subpage'       => "camera-$noCamera",
            'channel_id'   => $noCamera,
            'button'        => '',
            'isi'           => 'v_data_camera_alter',
        );

        $this->load->view('layout/wrapper', $data);
    }

    public function get_data_camera()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $search = $this->input->post('search')['value'];
        $order = $this->input->post('order')[0];
        $channel_id = $this->input->post('channel_id');
        $start_date = $this->input->post('start_date');
        $end_date = $this->input->post('end_date');

        $data = $this->Cctv_model->get_data_detail($start, $length, $search, array(
            'column' => $this->input->post('columns')[$order['column']]['data'],
            'dir' => $order['dir']
        ), $channel_id, $start_date, $end_date);

        $output = array(
            "draw" => $this->input->post('draw'),
            "recordsTotal" => $this->Cctv_model->count_all(),
            "recordsFiltered" => $this->Cctv_model->count_filtered($search, $channel_id, $start_date, $end_date),
            "data" => $data,
        );

        echo json_encode($output);
    }

    public function get_data_camera_alter()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $channel_id = $this->input->post('channel_id');
        $search = $this->input->post('search')['value'];
        $order = $this->input->post('order')[0];

        $data = $this->CctvAlterModel->get_data_detail($start, $length, $search, array(
            'column' => $this->input->post('columns')[$order['column']]['data'],
            'dir' => $order['dir']
        ), $channel_id);

        $output = array(
            "draw" => $this->input->post('draw'),
            "recordsTotal" => $this->CctvAlterModel->count_all(),
            "recordsFiltered" => $this->CctvAlterModel->count_filtered($search, $channel_id),
            "data" => $data,
        );

        echo json_encode($output);
    }

    public function ruang_operasi()
    {
        // Ambil daftar channel untuk dropdown
        $channels = $this->Cctv_model->get_channels_ruang_operasi();
        
        $data = array(
            'title'         => 'Ruang Operasi',
            'header'        => 'Ruang Operasi',
            'page'          => 'ruang_operasi',
            'subpage'       => 'ruang-operasi',
            'channels'      => $channels,
            'button'        => '',
            'isi'           => 'v_data_ruang_operasi',
        );

        $this->load->view('layout/wrapper', $data);
    }

    public function get_data_ruang_operasi()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $search = $this->input->post('search')['value'];
        $order = $this->input->post('order')[0];
        $start_date = $this->input->post('start_date');
        $end_date = $this->input->post('end_date');
        $channel_id = $this->input->post('channel_id');

        $data = $this->Cctv_model->get_data_ruang_operasi($start, $length, $search, array(
            'column' => $this->input->post('columns')[$order['column']]['data'],
            'dir' => $order['dir']
        ), $start_date, $end_date, $channel_id);

        $output = array(
            "draw" => $this->input->post('draw'),
            "recordsTotal" => $this->Cctv_model->count_all_ruang_operasi(),
            "recordsFiltered" => $this->Cctv_model->count_filtered_ruang_operasi($search, $start_date, $end_date, $channel_id),
            "data" => $data,
        );

        echo json_encode($output);
    }

    // Method untuk testing filtering channel - dapat diakses via browser untuk debugging
    public function test_channel_filter($channel_id = null)
    {
        $result = $this->Cctv_model->test_channel_filter($channel_id);

        echo "<h3>Test Channel Filter</h3>";
        echo "<p><strong>Channel ID:</strong> " . ($channel_id ? $channel_id : 'Semua (kecuali channel 4)') . "</p>";
        echo "<p><strong>Query:</strong> " . $result['query'] . "</p>";
        echo "<h4>Hasil:</h4>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Channel ID</th><th>Channel Name</th><th>Total Records</th></tr>";

        if (!empty($result['data'])) {
            foreach ($result['data'] as $row) {
                echo "<tr>";
                echo "<td>" . $row['channel_id'] . "</td>";
                echo "<td>" . $row['channel_name'] . "</td>";
                echo "<td>" . $row['total_records'] . "</td>";
                echo "</tr>";
            }
        } else {
            echo "<tr><td colspan='3'>Tidak ada data ditemukan</td></tr>";
        }

        echo "</table>";

        // Tampilkan juga daftar channel yang tersedia
        $channels = $this->Cctv_model->get_channels_ruang_operasi();
        echo "<h4>Channel yang Tersedia:</h4>";
        echo "<ul>";
        foreach ($channels as $channel) {
            echo "<li><a href='" . site_url('dashboard/test_channel_filter/' . $channel['channel_id']) . "'>";
            echo "Channel " . $channel['channel_id'] . " - " . $channel['channel_name'] . "</a></li>";
        }
        echo "</ul>";

        echo "<p><a href='" . site_url('dashboard/test_channel_filter') . "'>Test Semua Channel</a></p>";
        echo "<p><a href='" . site_url('dashboard/ruang_operasi') . "'>Kembali ke Ruang Operasi</a></p>";
    }
}
