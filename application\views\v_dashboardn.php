<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <title>J<PERSON>wal Dokter</title>
    <link href="https://fonts.googleapis.com/css?family=Poppins:400,500,700,800&display=swap" rel="stylesheet">
    <link href="assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/plugins/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="assets/plugins/perfectscroll/perfect-scrollbar.css" rel="stylesheet">
    <link href="assets/plugins/select2/select2.min.css" rel="stylesheet" >
    <link href="assets/plugins/DataTables/datatables.min.css" rel="stylesheet">   
    <link href="assets/css/darkmode.css" rel="stylesheet">
    
    <!-- Theme Styles -->
    <link href="assets/css/main.css" rel="stylesheet">
    <link href="assets/css/custom.css" rel="stylesheet">
</head>
<style>
    
    @media (orientation: portrait) {
        .testForm1 {
            margin-top: 30px;
        }
    }

    /* Pastikan tabel memenuhi area */
    .dataTables_wrapper {
        display: flex;
        flex-direction: column;
        height: auto;
    }

    /* Scroll akan menyesuaikan otomatis */
    .dataTables_scrollBody {
        max-height: calc(100vh - 120px); /* Dinamis berdasarkan tinggi layar */
        overflow-y: auto !important;
    }

    /* Untuk Chrome, Edge, dan Safari */
    .dataTables_scrollBody::-webkit-scrollbar {
        width: 8px; /* Lebar scrollbar */
    }

    .dataTables_scrollBody::-webkit-scrollbar-track {
        background: #304258; /* Warna latar belakang scrollbar */
        border-radius: 10px; /* Membuat sudut scrollbar lebih lembut */
    }

    .dataTables_scrollBody::-webkit-scrollbar-thumb {
        background: #EFB036; /* Warna thumb scrollbar */
        border-radius: 10px; /* Agar lebih smooth */
    }

    .dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
        background: #555; /* Warna saat di-hover */
    }

</style>
<body>
<div class="page-container">
    <div class="page-header">
        <nav class="navbar navbar-expand-lg d-flex justify-content-between">
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="darkModeToggle">
            </div>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="dataTablesToggle">
            </div>
            <div class="row" style="width:100%">
                <div class="col-md-4 col-sm-4 d-flex justify-content-start"><h3 class="mt-3"><img src="assets/images/dharmais.png" alt="" style="width:180px"></h3></div>
                <div class="col-md-4 col-sm-4 d-flex justify-content-center"><div id="time" style="font-size: 40px;"></div></div>
                <div class="col-md-4 col-sm-4 d-flex justify-content-end pull-right">
                    <div class="row">
                        <div class="col-12">
                            <h3 class="jadwalw" style="text-align: end;"><b>Jadwal Dokter</b></h3>
                        </div>
                        <div class="col-12">
                            <h3 class="row col-12 d-flex justify-content-end" id="date"></h3>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </div>
    <div class="page-content" style="margin-top:125px; margin-left:0px">
        <div class="main-wrapper p-0">
            <div class="row">
                <div class="col">
                    <div class="card testForm" style="display:none">
                        <div class="card-body">
                            <form id="formCariRuang">
                                <div class="row">
                                    <div class="col-md-6 col-sm-12">
                                        <div class="row">
                                            <div class="col-12">
                                                <span class="test bold">Ruangan</span>
                                                <select class="form-select RUANG_ASAL" id="RUANG_ASAL" aria-label="Default select example">
                                                </select><p></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-1 col-sm-12">
                                        <div class="row mt-3">
                                            <button type="submit" class="btn btn-primary d-flex justify-content-center cariRuang" id="cariRuang">
                                                <span class="bold">CARI</span class="bold">
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-1 col-sm-12">
                                        <div class="row mt-3">
                                            <button type="submit" class="btn btn-danger d-flex justify-content-center resetFilter" id="resetFilter">
                                                <span class="bold">RESET</span class="bold">
                                            </button>
                                        </div>
                                    </div>
                                    <span style="color:red">Harap tekan tombol Reset sebelum mengganti ruangan atau menampilkan seluruh ruangan*</span>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col">
                    <div class="card testForm1">
                        <div class="card-body">
                            <div class="table-responsive">
                            <table id="myTable" class="display mytable" style="width:100%">
                                <thead>
                                    <tr class="fs-3 fw-bold" style="text-align:center">
                                        <th class="putih" style="border-radius:10px 0 0 10px">No</th>
                                        <th class="putih" style="max-width:200px;width:200px;">Jadwal</th>
                                        <th class="putih">Nama Dokter</th>
                                        <th class="putih" style="width:300px">Ruangan</th>
                                        <th class="putih" style="max-width:85px">Kamar</th>
                                        <th class="putih" style="border-radius:0 10px 10px 0">Keterangan</th>
                                    </tr>
                                </thead>
                                <tbody class="fs-5 text-center">
                                </tbody>
                            </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <script src="assets/plugins/jquery/jquery-3.4.1.min.js"></script>
    <script src="https://unpkg.com/@popperjs/core@2"></script>
    <script src="assets/plugins/bootstrap/js/bootstrap.min.js"></script>
    <script src="https://unpkg.com/feather-icons"></script>
    <script src="assets/plugins/perfectscroll/perfect-scrollbar.min.js"></script>
    <script src="assets/plugins/DataTables/datatables.min.js"></script>
    <script src="assets/plugins/select2/select2.min.js"></script>
    <script src="assets/js/main.min.js"></script>
    <script src="assets/js/pages/datatables.js"></script>


    <script>

        if (navigator.userAgent.includes("TCL")) {
            document.body.style.transform = "scale(0.7)";
            document.body.style.transformOrigin = "top left";
            document.body.style.width = "142.86%";
        }
		$(document).ready(function() {

		var Ruang1   = $("#RUANG_ASAL option:selected").text();

        $('.RUANG_ASAL').on('change', function() {
            var Ruang1   = $("#RUANG_ASAL option:selected").text();
            $('#hello').text(Ruang1);
            // alert(Ruang1);
        });

        $('.main-wrapper').mousemove(function() {
            $('.testForm').show();
                setTimeout(function(){
                    if ($('.testForm').is(":hover")) {
                } else {
                    $('.testForm').hide();
            }
                },10000);
        });

        if ($('#dataTablesToggle').prop('checked')) {
            $('#RUANG_ASAL').select2({
                placeholder: '[ Pilih Ruangan]',
                width: '100%',
                ajax: {
                    dataType: 'json',
                    url: "<?php echo base_url('Dashboardn/getRuangan') ?>",
                    delay: 250,
                    data: function () {
                        return {
                            jenis: 1 
                        };
                    },
                    processResults: function (data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                }
            });
        }else{
            $('#RUANG_ASAL').select2({
                placeholder: '[ Pilih Ruangan]',
                width: '100%',
                ajax: {
                    dataType: 'json',
                    url: "<?php echo base_url('Dashboardn/getRuangan') ?>",
                    delay: 250,
                    data: function () {
                        return {
                            jenis: 2 
                        };
                    },
                    processResults: function (data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                }
            });
        }

        $('#dataTablesToggle').on('change', function () {
            $('#RUANG_ASAL').select2('destroy');
            if ($(this).prop('checked')) {
                $('#RUANG_ASAL').select2({
                    placeholder: '[ Pilih Ruangan]',
                    width: '100%',
                    ajax: {
                        dataType: 'json',
                        url: "<?php echo base_url('Dashboardn/getRuangan') ?>",
                        delay: 250,
                        data: function () {
                            return {
                                jenis: 1 
                            };
                        },
                        processResults: function (data) {
                            return {
                                results: data
                            };
                        },
                        cache: true
                    }
                });
            }else{
                $('#RUANG_ASAL').select2({
                    placeholder: '[ Pilih Ruangan]',
                    width: '100%',
                    ajax: {
                        dataType: 'json',
                        url: "<?php echo base_url('Dashboardn/getRuangan') ?>",
                        delay: 250,
                        data: function () {
                            return {
                                jenis: 2 
                            };
                        },
                        processResults: function (data) {
                            return {
                                results: data
                            };
                        },
                        cache: true
                    }
                });
            }
        });

		let table;

        function initDataTable(ajaxUrl, ajaxData = {}) {
            // Cek jika DataTable sudah ada, jika belum inisialisasi
            if ($.fn.DataTable.isDataTable('#myTable')) {
				$('#myTable').DataTable().clear();
				$('#myTable').DataTable().destroy();
			}
                // Inisialisasi DataTable pertama kali
                var table = $('#myTable').DataTable({
                    responsive: true,
                    pageLength: -1, // Tampilkan semua data
                    bLengthChange: true,
                    ordering: false,
                    order: [],
                    dom: 'rt',
                    scrollY: getScrollHeight(),
                    scrollCollapse: true,
                    ajax: {
                        url: ajaxUrl,
                        type: 'POST',
                        data: ajaxData
                    },
                    initComplete: function () {
                        setTimeout(autoScroll, 1000);

                        // Reload data setiap 60 detik
                        setInterval(function () {
                            table.ajax.reload(null, false);
                        }, 60000);
                    }
                });

                $(window).on('resize', function () {
                    var newHeight = getScrollHeight();
                    $('.dataTables_scrollBody').css('max-height', newHeight);
                });

                // Fungsi untuk mendapatkan tinggi scroll yang sesuai
                function getScrollHeight() {
                    var windowHeight = $(window).height(); // Tinggi layar
                    var headerHeight = $('.header').outerHeight(true) || 50; // Tinggi header (default 50)
                    var footerHeight = $('.footer').outerHeight(true) || 50; // Tinggi footer (default 50)
                    var margin = 300; // Tambahan margin

                    return (windowHeight - headerHeight - footerHeight - margin) + "px";
                }

                // Fungsi autoscroll yang stabil
                function autoScroll() {
                    var tableContainer = $('#myTable_wrapper .dataTables_scrollBody');
                    if (tableContainer.length === 0) {
                        console.warn("Elemen dataTables_scrollBody tidak ditemukan!");
                        return;
                    }

                    let scrollTop = 0; // Posisi awal
                    let direction = 1; // 1 untuk turun, -1 untuk naik
                    let scrollSpeed = 0.5; // Kecepatan per frame (px per step)
                    let frameRate = 16; // Waktu antar frame (ms)
                    
                    function scrollStep() {
                        let maxScroll = tableContainer.prop('scrollHeight') - tableContainer.innerHeight();

                        // Ubah arah jika mencapai batas
                        if (scrollTop >= maxScroll) {
                            direction = -1;
                        } else if (scrollTop <= 0) {
                            direction = 1;
                        }

                        // Update posisi scroll
                        scrollTop += direction * scrollSpeed;
                        tableContainer.scrollTop(scrollTop);

                        // Loop animasi
                        requestAnimationFrame(scrollStep);
                    }

                    // Mulai scroll
                    requestAnimationFrame(scrollStep);
                }

        }

		// Default load menggunakan `get_data_jadwal_dokter`
		$(document).ready(function () {
            // setTimeout(function() {
            //     location.reload();
            // }, 21600000);
            // Pulihkan nilai toggle dan filter dari localStorage
            const savedToggleState = localStorage.getItem('dataTablesToggleState') === 'true';
            const savedFilter = localStorage.getItem('RUANG_ASAL');

            // Setel status toggle berdasarkan localStorage
            $('#dataTablesToggle').prop('checked', savedToggleState);

            // Fungsi untuk inisialisasi DataTables
            function initializeDataTable(isChecked, filter) {
                let url;
                let params = {};

                if (isChecked) {
                    if (filter) {
                        $('#RUANG_ASAL').val(filter).trigger('change');
                        url = '<?php echo base_url("Dashboardn/get_data_jadwal_dokter_s") ?>';
                        params.RUANGAN = filter;
                    } else {
                        url = '<?php echo base_url("Dashboardn/get_data_jadwal_dokter") ?>';
                    }
                } else {
                    if (filter) {
                        $('#RUANG_ASAL').val(filter).trigger('change');
                        url = '<?php echo base_url("Dashboardn/get_data_jadwal_dokter_cs") ?>';
                        params.RUANGAN = filter;
                    } else {
                        url = '<?php echo base_url("Dashboardn/get_data_jadwal_dokter_c") ?>';
                    }
                }

                initDataTable(url, params);
            }

            // Inisialisasi DataTables berdasarkan status toggle terakhir
            initializeDataTable(savedToggleState, savedFilter);

            // Tangani perubahan toggle
            $('#dataTablesToggle').on('change', function () {
                const isChecked = $(this).prop('checked');

                // Simpan status toggle ke localStorage
                localStorage.setItem('dataTablesToggleState', isChecked);

                // Perbarui DataTables berdasarkan status toggle
                initializeDataTable(isChecked, savedFilter);
            });
        });



		// Pencarian dengan tombol cari
		$('.cariRuang').on('click', function (event) {
			event.preventDefault();

			// Simpan nilai filter ke localStorage
			const filterValue = $('#RUANG_ASAL').val();
			localStorage.setItem('RUANG_ASAL', filterValue);

            // $('#dataTablesToggle').on('change', function () {
                if ($('#dataTablesToggle').prop('checked')) {
                    // Load DataTable dengan URL baru dan data tambahan
                    initDataTable('<?php echo base_url('Dashboardn/get_data_jadwal_dokter_s') ?>', {
                        RUANGAN: filterValue
                    });
                }else{
                    initDataTable('<?php echo base_url('Dashboardn/get_data_jadwal_dokter_cs') ?>', {
                        RUANGAN: filterValue
                    });
                }
            // });
		});

		$('.resetFilter').on('click', function () {
			// Hapus nilai filter dari localStorage
			localStorage.removeItem('RUANG_ASAL');

			// Reset Select2 ke keadaan awal (kosong atau default)
			$('#RUANG_ASAL').val(null).trigger('change');
             location.reload();

            $('#dataTablesToggle').on('change', function () {
                if ($(this).prop('checked')) {
                    // Reload DataTable tanpa filter
                    initDataTable('<?php echo base_url('Dashboardn/get_data_jadwal_dokter') ?>');
                }else{
                    initDataTable('<?php echo base_url('Dashboardn/get_data_jadwal_dokter_c') ?>');
                }
            });
		});

		// function getPageLength() {
		// 	if (window.innerHeight > window.innerWidth) {
		// 		// Layar vertikal (portrait)
		// 		return 13; // Tampilkan 5 data pada layar vertikal
		// 	} else {
		// 		// Layar horizontal (landscape)
		// 		return 8; // Tampilkan 10 data pada layar horizontal
		// 	}
		// }

		// // Event listener untuk mendeteksi perubahan ukuran layar
		// $(window).resize(function() {
		// 	var newPageLength = getPageLength();
		// 	// Mengubah jumlah data yang ditampilkan berdasarkan orientasi layar
		// 	table.page.len(newPageLength).draw();
		// });

	});
	</script>

	<script type="text/javascript">
		window.setInterval(waktu, 1000);

		function waktu() {
			var dt = new Date();
			var dateOptions = {
				weekday: 'long',
				year: 'numeric',
				month: 'short',
				day: 'numeric'
			};

			var timeOptions = {
				hour: '2-digit',
				minute: '2-digit',
				hour12: false // Format 24 jam
			};

			// Format tanggal dalam bahasa Indonesia
			var formattedDate = dt.toLocaleDateString('id-ID', dateOptions).replace(/\//g, ' ');

			// Format waktu dan ganti titik dengan titik dua
			var formattedTime = dt.toLocaleTimeString('id-ID', timeOptions).replace(/\./g, ':');

			// Tampilkan tanggal dan jam di elemen yang berbeda
			document.getElementById("date").innerHTML = formattedDate;
			document.getElementById("time").innerHTML = formattedTime;
		}
	</script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const toggle = document.getElementById("darkModeToggle");
            const body = document.body;

            // Cek apakah dark mode sebelumnya aktif
            if (localStorage.getItem("dark-mode") === "enabled") {
                body.classList.add("dark-mode");
                toggle.checked = true;
            }

            // Aktifkan/Nonaktifkan dark mode saat tombol toggle diubah
            toggle.addEventListener("change", function () {
                if (toggle.checked) {
                    body.classList.add("dark-mode");
                    localStorage.setItem("dark-mode", "enabled");
                } else {
                    body.classList.remove("dark-mode");
                    localStorage.setItem("dark-mode", "disabled");
                }
            });
        });

    </script>
</body>
</html>
