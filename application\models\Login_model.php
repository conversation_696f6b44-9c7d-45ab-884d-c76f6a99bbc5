<?php
class Login_model extends CI_Model{
    protected $_table_name    = 'aplikasi.pengguna';
    protected $_primary_key   = 'ID';
    protected $_order_by      = 'ID';
    protected $_order_by_type = 'DESC';
    //Auth Login
    function auth($username,$password){
        $query= "SELECT
        ap.ID,
        ap.LOGIN,
        ap.PASSWORD,
        ap.NAMA,
        ap.NIP,
        PASSWORD (?) PASS,
        amu.ID_MENU,
        am.LABEL,
        GROUP_CONCAT( am.ID ) AKSES,
        am.LINK,
        pega.SMF,
        ref.DESKRIPSI SMF_DESC,
        prof.ID PROFESI_ID,
        prof.DESKRIPSI PROFESI_DESC 
    FROM
        aplikasi.pengguna ap
        LEFT JOIN akses_simrskd.MENU_USER amu ON amu.ID_USER = ap.ID
        LEFT JOIN akses_simrskd.MENU am ON am.ID = amu.ID_MENU
        LEFT JOIN MASTER.pegawai pega ON pega.NIP = ap.NIP
        LEFT JOIN MASTER.referensi ref ON ref.ID = pega.SMF 
        AND ref.JENIS = ?
        LEFT JOIN MASTER.referensi prof ON prof.ID = pega.PROFESI 
        AND prof.JENIS = ? 
    WHERE
        ap.LOGIN = ? 
    GROUP BY
        amu.ID_USER 
    ORDER BY
        am.SEQ DESC";
        $bind = $this->db->query($query, array($password, 26, 36, $username)); 
        return $bind;
    }

    function login(){
    $username = $this->input->post('username');
    $password = $this->input->post('password');
    $this->db->select('ap.ID,ap.LOGsIN,ap.PASSWORD,ap.NAMA,ap.NIP,PASSWORD("'.$password.'") PASS,amu.ID_MENU, am.LABEL
      ,GROUP_CONCAT(am.ID) AKSES, am.LINK, pega.SMF, ref.DESKRIPSI SMF_DESC 
      , prof.ID PROFESI_ID, prof.DESKRIPSI PROFESI_DESC');
    $this->db->from('aplikasi.pengguna ap');
    $this->db->join('akses_simrskd.MENU_USER amu', 'amu.ID_USER = ap.ID', 'left');
    $this->db->join('akses_simrskd.MENU am', 'am.ID = amu.ID_MENU', 'left');
    $this->db->join('master.pegawai pega', 'pega.NIP = ap.NIP', 'left');
    $this->db->join('master.referensi ref', 'ref.ID = pega.SMF AND ref.JENIS=26', 'left');
    $this->db->join('master.referensi prof', 'prof.ID = pega.PROFESI AND prof.JENIS=36', 'left');
    
    $this->db->where('LOGIN',$username);
    $this->db->group_by('amu.ID_USER');
    $this->db->order_by('am.SEQ', 'DESC');

    $query = $this->db->get()->row();
    if($query == "")
    {
      return array('status' => 204, 'message' => 'Username dan password tidak sesuai.');
    }else{
      $private_key     = 'KDFLDMSTHBWWSGCBH';
      $hashed_password = $query->PASSWORD;
      $passwordMD5     = MD5($private_key.MD5($password).$private_key);

      if(hash_equals($hashed_password,$passwordMD5) || $query->PASSWORD == $query->PASS)
      {

        $data = array('id' => $id , 'username' => $username, 'nama' => $nama, 'link' => $link);
        if($link == null){
          return array('status' => 204, 'message' => 'Akses belum diberikan hubungi SIMRS');
        }else{
          return array('status' => 200, 'message' => 'Successfully login.', 'data' => $data );
        }
      }else{
        return array('status' => 204, 'message' => 'Username dan password tidak sesuai.');
      }

    }
  }

        function cek_login(){
          $username = $this->input->post('username');
          $password = $this->input->post('password');
        $this->db->select('ap.ID,ap.LOGIN,ap.PASSWORD,ap.NAMA,ap.NIP,PASSWORD("'.$password.'") PASS,amu.ID_MENU, am.LABEL,GROUP_CONCAT(am.ID) AKSES, am.LINK, pega.SMF, ref.DESKRIPSI SMF_DESC, prof.ID PROFESI_ID, prof.DESKRIPSI PROFESI_DESC');
          $this->db->from('aplikasi.pengguna ap');
          $this->db->join('akses_simrskd.MENU_USER amu', 'amu.ID_USER = ap.ID', 'left');
          $this->db->join('akses_simrskd.MENU am', 'am.ID = amu.ID_MENU', 'left');
          $this->db->join('master.pegawai pega', 'pega.NIP = ap.NIP', 'left');
          $this->db->join('master.referensi ref', 'ref.ID = pega.SMF AND ref.JENIS=26', 'left');
          $this->db->join('master.referensi prof', 'prof.ID = pega.PROFESI AND prof.JENIS=36', 'left');
          
          $this->db->where('LOGIN',$username);
          $this->db->group_by('amu.ID_USER');
          $this->db->order_by('am.SEQ', 'DESC');
        $query = $this->db->get();
        $user = $query->row();

        if($user == "")
        {
            // return FALSE;
            return array('status' => 204, 'message' => 'Username dan password tidak sesuai.');
        }else{
            $private_key     = 'KDFLDMSTHBWWSGCBH';
            $hashed_password = $user->PASSWORD;
            $id              = $user->ID;
            $username        = $user->LOGIN;
            $nama            = $user->NAMA;
            $link            = 'masuk';
            $passwordMD5     = MD5($private_key.MD5($password).$private_key);

                if(hash_equals($hashed_password,$passwordMD5) || $user->PASSWORD == $user->PASS){
                  $data = array('id' => $id , 'username' => $username, 'nama' => $nama, 'link' => $link);
                    // return $query->result();
                    return array('status' => 200, 'message' => 'Successfully login.', 'data' => $data );
                }else{
                    // return FALSE;
                    return array('status' => 204, 'message' => 'Username dan password tidak sesuai.');
                }

        }
    }

}
?>