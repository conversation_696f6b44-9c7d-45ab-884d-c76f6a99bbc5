<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h3>Data CCTV - Depan <PERSON>pi</h3>
                <form id="filterDate">
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label for="startDate"><PERSON><PERSON></label>
                                <input type="text" class="form-control datepicker" id="startDate" placeholder="Pilih tanggal mulai">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label for="endDate">Tanggal Akhir</label>
                                <input type="text" class="form-control datepicker" id="endDate" placeholder="Pilih tanggal akhir">
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12 text-end">
                            <button type="button" class="btn btn-secondary" id="resetButton">Reset</button>
                        </div>
                    </div>
                </form>
                <div class="table table-responsive mt-5">
                    <table class="table table-hover" id="myTable">
                    <thead class="text-center">
                        <tr>
                            <th width="5%">No.</th>
                            <th width="30%">Nama Dokter</th>
                            <th>Kamera</th>
                            <th>Waktu</th>
                            <th>Akurasi</th>
                            <th>Gambar Kecil</th>
                            <th>Gambar Besar</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="imagePopup" class="image-popup">
    <span class="close-popup">&times;</span>
    <div class="popup-content">
        <img id="popupImage" class="popup-img" src="" alt="Gambar">
    </div>
    <button class="reset-zoom-btn" onclick="resetZoom()">Reset Zoom</button>
</div>

<style>
    .image-popup {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        z-index: 1000;
    }
    .popup-content {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: auto;
    }
    .popup-img {
        max-height: 90vh;
        max-width: 90vw;
        min-height: 50vh;
        min-width: auto;
        object-fit: contain;
        transform-origin: center;
        transition: transform 0.1s ease;
        cursor: zoom-in;
    }

    .close-popup {
        position: absolute;
        top: 15px;
        right: 25px;
        color: #f1f1f1;
        font-size: 40px;
        font-weight: bold;
        cursor: pointer;
    }

    .reset-zoom-btn {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        padding: 8px 16px;
        background: #fff;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .img-thumbnail.preview-image {
        cursor: pointer !important;
    }
</style>

<script>
    $(document).ready(function() {
        // Initialize datepicker
        $('.datepicker').bootstrapMaterialDatePicker({
            weekStart: 0,
            time: false,
            clearButton: true,
            format: 'YYYY-MM-DD',
            maxDate: new Date()
        });

        // Initialize DataTable
        var table = $('#myTable').DataTable({
            "processing": true,
            "serverSide": true,
            "ajax": {
                "url": "<?php echo site_url('dashboard/get_data_camera'); ?>",
                "type": "POST",
                "data": function(d) {
                    d.channel_id = <?php echo $channel_id; ?>;
                    d.start_date = $('#startDate').val();
                    d.end_date = $('#endDate').val();
                }
            },
            "order": [[3, "desc"]], 
            "columns": [{
                    "data": "alarm_id"
                },
                {
                    "data": "name"
                },
                {
                    "data": "channel_name"
                },
                {
                    "data": "capture_time",
                    "render": function(data, type, row) {
                        var date = new Date(data * 1000);
                        return date.toLocaleString();
                    }
                },
                {
                    "data": "similarity",
                    "render": function(data, type, row) {
                        return data + " %";
                    }
                },
                {
                    "data": "small_picture_url",
                    "render": function(data, type, row) {
                        var fullUrl = 'http://192.168.7.60/cron/cctv/' + data;
                        return '<img src="' + fullUrl + '" width="50" height="50" class="img-thumbnail preview-image" data-url="' + fullUrl + '">';
                    }
                },
                {
                    "data": "big_picture_url",
                    "render": function(data, type, row) {
                        var fullUrl = 'http://192.168.7.60/cron/cctv/' + data;
                        return '<img src="' + fullUrl + '" width="100" height="100" class="img-thumbnail preview-image" data-url="' + fullUrl + '">';
                    }
                }
            ]
        });



        // Reset button event
        $('#resetButton').click(function(e) {
            e.preventDefault();
            $('#startDate').val('');
            $('#endDate').val('');
            Swal.fire({
                title: 'Mohon Tunggu...',
                text: 'Sedang mengambil data...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            table.ajax.reload(function() {
                Swal.close();
            });
        });

        // Auto reload when date changes
        $('.datepicker').on('change', function() {
            if ($('#startDate').val() && $('#endDate').val()) {
                Swal.fire({
                    title: 'Mohon Tunggu...',
                    text: 'Sedang mengambil data...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                table.ajax.reload(function() {
                    Swal.close();
                });
            }
        });

    let scale = 1;
    const ZOOM_SPEED = 0.1;
    const MAX_ZOOM = 4;
    const MIN_ZOOM = 0.5;
    
    // Event listener untuk menampilkan gambar
    $(document).on('click', '.preview-image', function() {
        var imgUrl = $(this).data('url');
        $('#popupImage').attr('src', imgUrl);
        $('#imagePopup').fadeIn();
        scale = 1;
        $('#popupImage').css('transform', `scale(${scale})`);
    });
    
    // Close popup
    $('.close-popup').click(function() {
        $('#imagePopup').fadeOut();
    });
    
    // Close with Escape key
    $(document).keydown(function(e) {
        if (e.key === "Escape") {
            $('#imagePopup').fadeOut();
        }
    });
    // Zoom functionality
    $('.popup-content').on('wheel', function(e) {
        e.preventDefault();
        const delta = e.originalEvent.deltaY;
        const img = $('#popupImage')[0];
        const rect = img.getBoundingClientRect();
        const offsetX = e.clientX - rect.left;
        const offsetY = e.clientY - rect.top;
        
        if (delta < 0) {
            scale = Math.min(scale + ZOOM_SPEED, MAX_ZOOM);
        } else {
            scale = Math.max(scale - ZOOM_SPEED, MIN_ZOOM);
        }
        
        $('#popupImage').css({
            'transform': `scale(${scale})`,
            'transform-origin': `${(offsetX / rect.width) * 100}% ${(offsetY / rect.height) * 100}%`
        });
    });
    // Double click zoom
    $('#popupImage').on('dblclick', function(e) {
        const img = this;
        const rect = img.getBoundingClientRect();
        const offsetX = e.clientX - rect.left;
        const offsetY = e.clientY - rect.top;
        
        if (scale < MAX_ZOOM) {
            scale = Math.min(scale + 1, MAX_ZOOM);
        } else {
            scale = MIN_ZOOM;
        }
        
        // Update cursor based on scale
        $(this).css({
            'transform': `scale(${scale})`,
            'transform-origin': `${(offsetX / rect.width) * 100}% ${(offsetY / rect.height) * 100}%`,
            'cursor': scale >= MAX_ZOOM ? 'zoom-out' : 'zoom-in'
        });
    });
    // Update cursor on wheel zoom
    $('.popup-content').on('wheel', function(e) {
        e.preventDefault();
        const delta = e.originalEvent.deltaY;
        const img = $('#popupImage')[0];
        const rect = img.getBoundingClientRect();
        const offsetX = e.clientX - rect.left;
        const offsetY = e.clientY - rect.top;
        
        if (delta < 0) {
            scale = Math.min(scale + ZOOM_SPEED, MAX_ZOOM);
        } else {
            scale = Math.max(scale - ZOOM_SPEED, MIN_ZOOM);
        }
        
        // Update cursor based on scale
        $(img).css({
            'transform': `scale(${scale})`,
            'transform-origin': `${(offsetX / rect.width) * 100}% ${(offsetY / rect.height) * 100}%`,
            'cursor': scale >= MAX_ZOOM ? 'zoom-out' : 'zoom-in'
        });
    });
    });
    
    function resetZoom() {
        scale = 1;
        $('#popupImage').css({
            'transform': `scale(${scale})`,
            'transform-origin': 'center center'
        });
    }
</script>