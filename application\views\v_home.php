<div class="row">
	<div class="col-lg-12">
		<div class="card">
			<div class="card-body">
				<table class="table table-hover" id="myTable">
					<thead class="text-center">
                        <tr>
                            <th width="10px">No.</th>
                            <th><PERSON><PERSON></th>
                            <th width="30px"><PERSON><PERSON></th>
                            <th width="30px">No.Counter</th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th width="30px">Status</th>
                            <th>#</th>
						</tr>
					</thead>
					<tbody>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>


<script>
	$(document).ready(function () {

        $('.DOKTER').select2({
            placeholder: '[ <PERSON>lih <PERSON>]',
            width: '100%',
            ajax: {
                dataType: 'json',
                url: "<?php echo base_url('Tambah/getDokter') ?>",
                delay: 250,
                processResults: function (data) {
                return {
                    results: data
                };
                },
                cache: true
            }
        });

        $('#RUANG_ASAL').select2({
            placeholder: '[ <PERSON><PERSON><PERSON>]',
            width: '100%',
            ajax: {
                dataType: 'json',
                url: "<?php echo ('Tambah/getRuangan') ?>",
                delay: 250,
                processResults: function (data) {
                return {
                    results: data
                };
                },
                cache: true
            }
        });

        $('#myTable').DataTable().clear();
        $('#myTable').DataTable().destroy();
        $('#myTable').DataTable({
            // "responsive": true,
            "pageLength" : 10,
            // "processing": true,
            // "serverSide": true,
            "bLengthChange": true,
            "ordering": false,
            "order": [],
            "columnDefs": [
                {
                    "targets": [ 2,3,4 ],
                    "className": "text-center",
                }
            ],
            "language": {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next":       "Selanjutnya",
                    "previous":   "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },
            
            lengthMenu: [[10, 20, 30, 40, 50], [10, 20, 30, 40, 50]],
            ajax: {
                url: '<?php echo base_url('Tambah/get_data_jadwal')?>',
                type: 'POST'
            },
        });


	});

</script>
</body>

</html>
