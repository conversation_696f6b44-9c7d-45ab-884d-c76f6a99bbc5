<div class="row">
	<div class="col-lg-12">
		<div class="card">
			<div class="card-body">
				<h3>Tambah Jadwal</h3>
                <div class="row my-3">
                    <form id="formJadwal" action="#" method="POST">
                        <div class="col-12">
                            <div class="row">
                                <div class="form-group col-6">
                                    <h4 class="col-form-label"><PERSON><PERSON></h4>
                                    <select class="form-control select2 DOKTER" name="DOKTER" id="DOKTER"></select>
                                </div>
                                <div class="form-group col-2">
                                    <h4 class="col-form-label">Kamar</h4>
                                    <input class="form-control" type="text" id="RUANGAN" name="RUANGAN">
                                </div>
                                <div class="form-group mb-3 col-2">
                                    <h4 class="col-form-label">No.Counter</h4>
                                    <input class="form-control" type="number" id="COUNTER" name="COUNTER">
                                </div>
                                <div class="form-group mb-3 col-2">
                                    <h4 class="col-form-label"><PERSON><PERSON>an</h4>
                                    <select class="form-select RUANG_ASAL" id="RUANG_ASAL" name="RUANG_ASAL" aria-label="Default select example"></select>
                                </div>
                            </div>
                        </div>
                        <center><button type="submit" class="btn btn-primary simpanJadwal mb-4" id="simpanJadwal">Simpan</button></center>
                    </form>
                </div>
				<table class="table table-hover" id="myTable">
					<thead class="text-center">
                        <tr>
                            <th width="10px">No.</th>
                            <th>Nama Dokter</th>
                            <th width="30px">Kamar</th>
                            <th width="30px">No.Counter</th>
                            <th>Ruangan</th>
                            <th width="30px">Status</th>
                            <th>#</th>
						</tr>
					</thead>
					<tbody>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>


<script>
	$(document).ready(function () {

        $('.DOKTER').select2({
            placeholder: '[ Pilih Dokter]',
            width: '100%',
            ajax: {
                dataType: 'json',
                url: "<?php echo base_url('Tambah/getDokter') ?>",
                delay: 250,
                processResults: function (data) {
                return {
                    results: data
                };
                },
                cache: true
            }
        });

        $('#RUANG_ASAL').select2({
            placeholder: '[ Pilih Ruangan]',
            width: '100%',
            ajax: {
                dataType: 'json',
                url: "<?php echo ('Tambah/getRuangan') ?>",
                delay: 250,
                processResults: function (data) {
                return {
                    results: data
                };
                },
                cache: true
            }
        });

        $('#myTable').DataTable().clear();
        $('#myTable').DataTable().destroy();
        $('#myTable').DataTable({
            // "responsive": true,
            "pageLength" : 10,
            // "processing": true,
            // "serverSide": true,
            "bLengthChange": true,
            "ordering": false,
            "order": [],
            "columnDefs": [
                {
                    "targets": [ 2,3,4 ],
                    "className": "text-center",
                }
            ],
            "language": {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next":       "Selanjutnya",
                    "previous":   "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },
            
            lengthMenu: [[10, 20, 30, 40, 50], [10, 20, 30, 40, 50]],
            ajax: {
                url: '<?php echo base_url('Tambah/get_data_jadwal')?>',
                type: 'POST'
            },
        });

        $('#simpanJadwal').on('click', function(event){
            event.preventDefault();
            swal.fire({
                title: "Yakin Ingin Simpan?",
                text: "Data akan disimpan",
                type: "warning",
                showConfirmButton:true,
                showCancelButton: true,
                confirmButtonText: 'Ya',
                cancelButtonText: 'Batal',
                focusConfirm:true
                }).then(result => {
                if (result.value) {
                    var formJadwal = $("#formJadwal").serializeArray(); 
                    $.ajax({
                    dataType:'json',
                    url: "<?php echo base_url('Tambah/simpanJadwal') ?>",
                    method: "POST",
                    data: formJadwal,
                        success: function(data) {
                            if(data.status == 'success'){
                                swal.fire({
                                title: "Berhasil Simpan",
                                type: "success",
                                }).then(function(){
                                    $("#formJadwal").trigger("reset");
                                    $('#myTable').DataTable().ajax.reload();
                                    toastr.success("Berhasil Simpan");
                                });
                            }else{
                                swal.fire({
                                title: "Gagal Simpan",
                                type: "error",
                                });
                            }
                        }
                    });
                        
                } else if(result.dismiss === swal.DismissReason.cancel){
                    swal.fire("Batal!", '', 'info');
                }
            });
        });

        // -------------------------------------------------------STATUS
        $('#myTable').on('click', '.selesai', function () {
        var ID = $(this).attr('data');
            // alert(ID);
        event.preventDefault();
        swal.fire({
            title: "Yakin Ingin Non-Aktif?",
            text: "Status Dokter menjadi Selesai Praktek",
            type: 'warning',
            showConfirmButton:true,
            showCancelButton: true,
            confirmButtonText: 'Ya',
            cancelButtonText: 'Batal',
            focusConfirm:true
            }).then(result => {
                if (result.value) {
                    $.ajax({
                    dataType:'json',
                    url: "<?php echo base_url('Tambah/selesai') ?>",
                    method: "POST",
                    data: {ID:ID},
                        success: function(data) {
                            if(data.status == 'success'){
                                swal.fire({
                                title: "Berhasil Non-Aktif",
                                type: 'success',
                                }).then(function() {
                                    $('#myTable').DataTable().ajax.reload();
                            }, 1000);
                            }else{
                                swal.fire({
                                title: "Gagal Non-Aktif",
                                type: 'error',
                                });
                            }
                        }
                    });
                        
                } else if(result.dismiss === swal.DismissReason.cancel){
                    swal.fire("Batal!", '', 'info');
                }
            });
        });

        $('#myTable').on('click', '.praktek', function () {
        var ID = $(this).attr('data');
            // alert(ID);
        event.preventDefault();
        swal.fire({
            title: "Yakin Ingin Aktifkan?",
            text: "Status Dokter menjadi Sedang Praktek",
            type: 'warning',
            showConfirmButton:true,
            showCancelButton: true,
            confirmButtonText: 'Ya',
            cancelButtonText: 'Batal',
            focusConfirm:true
            }).then(result => {
                if (result.value) {
                    $.ajax({
                    dataType:'json',
                    url: "<?php echo base_url('Tambah/praktek') ?>",
                    method: "POST",
                    data: {ID:ID},
                        success: function(data) {
                            if(data.status == 'success'){
                                swal.fire({
                                title: "Berhasil Mengaktifkan",
                                type: 'success',
                                }).then(function() {
                                    $('#myTable').DataTable().ajax.reload();
                            }, 1000);
                            }else{
                                swal.fire({
                                title: "Gagal Mengaktifkan",
                                type: 'error',
                                });
                            }
                        }
                    });
                        
                } else if(result.dismiss === swal.DismissReason.cancel){
                    swal.fire("Batal!", '', 'info');
                }
            });
        });

        // -------------------------------------------------------HAPUS
        $('#myTable').on('click', '.hapus', function () {
        var ID = $(this).attr('data');
            // alert(ID);
        event.preventDefault();
        swal.fire({
            title: "Yakin Ingin Hapus?",
            type: 'warning',
            showConfirmButton:true,
            showCancelButton: true,
            confirmButtonText: 'Ya',
            cancelButtonText: 'Batal',
            focusConfirm:true
            }).then(result => {
                if (result.value) {
                    $.ajax({
                    dataType:'json',
                    url: "<?php echo base_url('Tambah/hapus') ?>",
                    method: "POST",
                    data: {ID:ID},
                        success: function(data) {
                            if(data.status == 'success'){
                                swal.fire({
                                title: "Berhasil Menghapus",
                                type: 'success',
                                }).then(function() {
                                    $('#myTable').DataTable().ajax.reload();
                            }, 1000);
                            }else{
                                swal.fire({
                                title: "Gagal Menghapus",
                                type: 'error',
                                });
                            }
                        }
                    });
                        
                } else if(result.dismiss === swal.DismissReason.cancel){
                    swal.fire("Batal!", '', 'info');
                }
            });
        });


	});

</script>
</body>

</html>
