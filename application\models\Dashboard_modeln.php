<?php
class Dashboard_modeln extends CI_Model{

    function listRuangan($JENISBANG){
        if ($this->input->get('q')) {
            $this->db->like('r.DESKRIPSI ', $this->input->get('q'));
        }
        $this->db->select('*');
        $this->db->from('master.ruangan r');
        $this->db->where('r.JENIS = 5 AND r.STATUS = 1 AND r.ID NOT IN (105020101, 105020102)');
        if ($JENISBANG == 1) {
            $this->db->where('r.JENIS_KUNJUNGAN IN (1,13,14)');
        }else{
             $this->db->where('r.JENIS_KUNJUNGAN = 15');
        }
        $this->db->order_by('r.DESKRIPSI', 'ASC');
        
        $query = $this->db->get();
        return $query;
    }

    function datatablesJadwal($JENISBANG, $RUANGAN)
    {
        $jamSeka<PERSON> = date('G');
        $hasil= "SELECT
                rmj.ID ID_JADWAL,
                DATE_FORMAT( rmj.TANGGAL, '%d-%m-%Y' ) TGL_JADWAL,
                rmj.DOKTER ID_DOKTER,
                master.getNamaLengkapPegawai ( md.NIP ) DOKTER,
                smf.ID ID_SMF,
                smf.DESKRIPSI SMF,
                dd.KAMAR,
                mr.ID ID_RUANG,
                mr.DESKRIPSI RUANG_LAYANAN,
                dd.ID ID_DD,
                dd.ID_JADWAL,
                rmj.AWAL,
                rmj.AKHIR,
                CONCAT( LPAD( rmj.AWAL, 2, '0' ), '.00 - ', LPAD( rmj.AKHIR, 2, '0' ), '.00' ) JAM,
                ket.DESKRIPSI KETERANGAN,
                dd.STATUS STATUS_DOKTER,
                dd.CREATED_AT TANGGAL_INPUT_STATUS,
                dd.NOTE_DISPLAY,
                (
                SELECT
                    CASE 
                        WHEN asn.NORM IS NOT NULL THEN 'ASN'
                        ELSE COALESCE(rmp.NOKONTROLDOKTER, '')
                    END AS hasil
                FROM
                    remun_medis.perjanjian rmp
                    LEFT JOIN remun_medis.pelayanan_pasien lp ON lp.idbooking = rmp.ID 
                    LEFT JOIN master.pasien_asnkemenkes asn ON asn.NORM = rmp.NOMR AND asn.STATUS =1
                WHERE
                    rmp.ID_RUANGAN = rmj.RUANGAN 
                    AND rmp.ID_DOKTER = rmj.DOKTER 
                    AND rmp.TANGGAL = rmj.TANGGAL 
                    AND rmp.STATUS = 1 
                    AND rmp.RENCANA = 1 
                    AND lp.id IS NOT NULL 
                ORDER BY
                    lp.created_at DESC 
                    LIMIT 1 
                ) ANTRIAN_DILAYANI,
                (
                SELECT
                    lp.created_at 
                FROM
                    remun_medis.perjanjian rmp
                    LEFT JOIN remun_medis.pelayanan_pasien lp ON lp.idbooking = rmp.ID 
                WHERE
                    rmp.ID_RUANGAN = rmj.RUANGAN 
                    AND rmp.ID_DOKTER = rmj.DOKTER 
                    AND rmp.TANGGAL = rmj.TANGGAL 
                    AND rmp.STATUS = 1 
                    AND rmp.RENCANA = 1 
                    AND lp.id IS NOT NULL 
                ORDER BY
                    lp.created_at DESC 
                    LIMIT 1 
                ) TANGGAL_KLIK,
                (
                SELECT
                    count( lp.id ) 
                FROM
                    remun_medis.perjanjian rmp
                    LEFT JOIN remun_medis.pelayanan_pasien lp ON lp.idbooking = rmp.ID 
                WHERE
                    rmp.ID_RUANGAN = rmj.RUANGAN 
                    AND rmp.ID_DOKTER = rmj.DOKTER 
                    AND rmp.TANGGAL = rmj.TANGGAL 
                    AND rmp.STATUS = 1 
                    AND rmp.RENCANA = 1 
                    AND lp.id IS NOT NULL 
                ) JUMLAH_DILAYANI 
            FROM
                remun_medis.jadwal rmj
                LEFT JOIN master.ruangan mr ON mr.ID = rmj.RUANGAN
                LEFT JOIN master.dokter md ON md.ID = rmj.DOKTER
                LEFT JOIN master.pegawai mpeg ON mpeg.NIP = md.NIP
                LEFT JOIN db_layanan.display_dokter dd ON dd.ID_JADWAL = rmj.ID
                LEFT JOIN master.referensi ket ON ket.ID = dd.STATUS 
                AND ket.JENIS = 129 
                AND ket.STATUS = 1
                LEFT JOIN master.referensi smf ON smf.ID = mpeg.SMF 
                AND smf.JENIS = 26 
                AND smf.STATUS = 1 
            WHERE
                rmj.STATUS = ?
                AND rmj.TANGGAL = CURDATE() 
                AND ( ket.ID IS NULL OR ket.ID != 6 ) 
                AND mr.ID NOT IN ( 105020101, 105020102 ) 
                AND rmj.AKHIR >= ?";

                if ($JENISBANG == 1) {
                    $hasil .= " AND mr.JENIS_KUNJUNGAN IN ( 1, 13, 14 ) ";
                }else{
                     $hasil .= " AND mr.JENIS_KUNJUNGAN = 15 ";
                }
                if ($RUANGAN != null) {
                    $hasil .= " AND mr.ID = ?";
                }

            $hasil .= "ORDER BY
                CASE 
                    WHEN rmj.RUANGAN = '105020704' THEN 1 
                    WHEN rmj.RUANGAN = '105020705' THEN 2 
                    WHEN rmj.RUANGAN = '105020708' THEN 3 
                    WHEN rmj.RUANGAN = '105020706' THEN 4 
                    WHEN rmj.RUANGAN = '105020201' THEN 5 
                    WHEN rmj.RUANGAN = '105060101' THEN 6 
                    ELSE 7 
                END,
                rmj.RUANGAN,
                mr.DESKRIPSI ASC,
                rmj.AWAL ASC,
                mpeg.NAMA ASC";
        if ($RUANGAN != null) {
            $bind = $this->db->query($hasil, array(1, $jamSekarang, $RUANGAN));
        } else {
            $bind = $this->db->query($hasil, array(1, $jamSekarang));
        }
        return $bind;
    }

}
?>
