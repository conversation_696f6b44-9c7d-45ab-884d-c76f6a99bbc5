<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Login extends CI_Controller {

    public function index()
    {
        //load view form login
        $this->load->view('v_login');
    }

    function signin(){
        $result = $this->Login_model->cek_login();
    
        if(isset($result['data'])){
          $data = $result['data'];
    
          $session = array(
            'id'        => $data['id'],
            'username'  => $data['username'],
            'nama'      => $data['nama'],
            'link'      => 'masuk',
            'logged_in' => TRUE,
          );
          $this->session->set_userdata($session);
        }
        echo json_encode($result);
      }

    public function cek_login()
    {
        $this->db->trans_begin();
        $username = $this->input->post('username');
        $password = $this->input->post('password');

        //cek login via model
        $cek = $this->Login_model->cek_login($username, $password);

        if(!empty($cek)){

            foreach($cek as $user) {

                //looping data user
                $session_data = array(
                    'id_user'   => $user->ID,
                    'username'  => $user->LOGIN,
                    'nama_lengkap' => $user->NAMA,
                );
                //buat session berdasarkan user yang login
                $this->session->set_userdata($session_data);

            }
              $result = array('status' => 'success');
              echo json_encode($result);

        } else {
            $result = array('status' => 'error');
            echo json_encode($result);

        }

    }

	public function keluar()
	{
		unset($_SESSION);
		$this->session->sess_destroy();
		redirect(base_url('login'));
	}

	public function check_session(){
    $session = $_SESSION;

    echo json_encode($session);
  }

}