/* Tambahkan ke main.css atau dark-mode.css */
body.dark-mode {
    background-color: #1c2d3d; /* <PERSON>tar belakang utama halaman */
    /* color: #ffffff; */
}

body.dark-mode .navbar {
    background-color: #304258;
    color: #ffffff;
    border-bottom: 1px solid #333333;
}

body.dark-mode .navbar .nav-link {
    color: #ffffff;
}

body.dark-mode .navbar .nav-link:hover {
    color: #cccccc;
}

body.dark-mode .card {
    background-color: #304258;
    color: #ffffff;
    border: 1px solid #333333;
}

body.dark-mode .btn-primary {
    background-color: #6200ea;
    border-color: #6200ea;
}

body.dark-mode .btn-danger {
    background-color: #b00020;
    border-color: #b00020;
}

body.dark-mode .form-select {
    background-color: #304258;
    color: #ffffff;
    border: 1px solid #333333;
}

body.dark-mode .table {
    background-color: #304258;
    color: #ffffff;
}

body.dark-mode table.mytable thead {
    background-color: #EFB036 !important;
    color: #333333 !important;
}

body.dark-mode .table tbody tr {
    border-bottom: 1px solid #444444;
}

body.dark-mode .table tbody tr:hover {
    background-color: #333333;
}

body.dark-mode .page-header::before {
    background: #1c2d3d; /* Warna latar belakang sesuai tema dark mode */
    border-bottom: 1px solid #333333; /* Opsional: tambahkan garis bawah untuk estetika */
    opacity: 0.9; /* Atur transparansi jika diperlukan */
}

.jadwalw{
    color: #EFB036;
}

.jadwals{
    background-color: #EFB036;
    color: #1c2d3d;
    
}
.form-check-input:checked {
    background-color:#EFB036 !important;
    border-color:#EFB036 !important;
}
/* 
.putih{
    color: #ffffff !important;
} */

