<?php
defined('BASEPATH') or exit('No direct script access allowed');

class CctvAlterModel extends CI_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    public function get_data_detail($start, $length, $search, $order, $channel_id)
    {
        $this->db->select('*');
        $this->db->from('db_cctv_ok.face_pass_records');
        $this->db->where('channel_id', $channel_id);
        $this->db->where('similarity >', 50);

        if (!empty($search)) {
            $this->db->group_start(); // Start grouping
            $this->db->like('channel_name', $search);
            $this->db->or_like('person_name', $search);
            $this->db->group_end(); // End grouping
        }

        if (!empty($order)) {
            $this->db->order_by($order['column'], $order['dir']);
        }

        $this->db->limit($length, $start);
        $query = $this->db->get();
        return $query->result_array();
    }

    public function count_all()
    {
        return $this->db->count_all('db_cctv_ok.face_pass_records');
    }

    public function count_filtered($search, $channel_id)
    {
        $this->db->select('*');
        $this->db->from('db_cctv_ok.face_pass_records');
        $this->db->where('channel_id', $channel_id);
        $this->db->where('similarity >', 50);

        if (!empty($search)) {
            $this->db->group_start(); // Start grouping
            $this->db->like('channel_name', $search);
            $this->db->or_like('person_name', $search);
            $this->db->group_end(); // End grouping
        }

        return $this->db->count_all_results();
    }
}
