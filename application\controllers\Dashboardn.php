<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboardn extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
    }

    public function index()
	{
		$this->load->view('v_dashboardn');
	}

    function getRuangan()
  	{
        $JENISBANG = $this->input->get('jenis');;
        $result = $this->Dashboard_modeln->listRuangan($JENISBANG)->result_array();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function get_data_jadwal_dokter(){
		$draw   = intval($this->input->POST("draw"));
		$start  = intval($this->input->POST("start"));
		$length = intval($this->input->POST("length"));

        $JENISBANG = 1;
        $RUANGAN = null;
		$listJadwal = $this->Dashboard_modeln->datatablesJadwal($JENISBANG, $RUANGAN);
	
		$data = array();
		$no = 1;
        foreach ($listJadwal->result() as $LJ) {

            $jamSekarang = date("G");
            $STATUS = '';
            $KAMAR = $LJ->KAMAR;

            if($LJ->AKHIR <= $jamSekarang){
                $STATUS = 'Selesai';
                $KAMAR = '';
            }elseif(!empty($LJ->ANTRIAN_DILAYANI && empty($LJ->STATUS_DOKTER) && $LJ->AWAL <= $jamSekarang && $LJ->AKHIR >= $jamSekarang)){
                if($LJ->ANTRIAN_DILAYANI == "ASN"){
                    $STATUS = '<h5 style="color:#02B891"> Sedang Melayani Pasien</h5>';
                }else{
                    $STATUS = '<h5 style="color:#02B891"> Sedang Melayani Pasien No.Urut ('.$LJ->ANTRIAN_DILAYANI.')</h5>';
                }
                if(empty($LJ->STATUS_DOKTER) && !empty($LJ->KAMAR)){
                    $KAMAR = $LJ->KAMAR;
                }else{
                    $KAMAR ='';
                }
            }elseif(!empty($LJ->STATUS_DOKTER)){
                if($LJ->STATUS_DOKTER == "4"){
                    $STATUS = '<h5>Terlambat ('.$LJ->NOTE_DISPLAY.')</h5>';
                    $KAMAR = $LJ->KAMAR;
                }elseif($LJ->STATUS_DOKTER == "5"){
                    $STATUS = '<h5>Batal ('.$LJ->NOTE_DISPLAY.')</h5>';
                    $KAMAR = $LJ->KAMAR;
                }elseif($LJ->STATUS_DOKTER =="6"){
                    $STATUS = '<h5>Selesai</h5>';
                    $KAMAR = $LJ->KAMAR;
                }
            }elseif($LJ->AWAL <= $jamSekarang && $LJ->AKHIR >= $jamSekarang && empty($LJ->ANTRIAN_DILAYANI)){
                $STATUS = '<h5>Terlambat</h5>';
                $KAMAR = $LJ->KAMAR;
            }elseif($LJ->AWAL > $jamSekarang){
                $STATUS = '<h5>Terjadwal</h5>';
                $KAMAR = $LJ->KAMAR;
            }
                
            $data[] = array(
                $no,
                '<h4 class="jadwalw">'. sprintf('%02d:00', $LJ->AWAL) . " - " . sprintf('%02d:00', $LJ->AKHIR) . '</h4>',
                '<h5 class="text-start">' . $LJ->DOKTER . '</h5>',
                $LJ->RUANG_LAYANAN,
                '<h4 class="jadwalw">' . $KAMAR . '</h4>',
                $STATUS,
            );
            $no++;
        }
	
		$output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listJadwal->num_rows(),
            "recordsFiltered" => $listJadwal->num_rows(),
            "data"            => $data
		);
		echo json_encode($output);
	}


    public function get_data_jadwal_dokter_s(){
        $draw   = intval($this->input->POST("draw"));
        $start  = intval($this->input->POST("start"));
        $length = intval($this->input->POST("length"));
    
        $JENISBANG = 1;
        $RUANGAN = $this->input->post('RUANGAN');
        $listJadwal = $this->Dashboard_modeln->datatablesJadwal($JENISBANG, $RUANGAN);
    
        $data = array();
		$no = 1;
        foreach ($listJadwal->result() as $LJ) {

            $jamSekarang = date("G");
            $STATUS = '';
            $KAMAR = $LJ->KAMAR;

            if($LJ->AKHIR <= $jamSekarang){
                $STATUS = 'Selesai';
                $KAMAR = '';
            }elseif(!empty($LJ->ANTRIAN_DILAYANI && empty($LJ->STATUS_DOKTER) && $LJ->AWAL <= $jamSekarang && $LJ->AKHIR >= $jamSekarang)){
                if($LJ->ANTRIAN_DILAYANI == "ASN"){
                    $STATUS = '<h5 style="color:#02B891"> Sedang Melayani Pasien</h5>';
                }else{
                    $STATUS = '<h5 style="color:#02B891"> Sedang Melayani Pasien No.Urut ('.$LJ->ANTRIAN_DILAYANI.')</h5>';
                }
                if(empty($LJ->STATUS_DOKTER) && !empty($LJ->KAMAR)){
                    $KAMAR = $LJ->KAMAR;
                }else{
                    $KAMAR ='';
                }
            }elseif(!empty($LJ->STATUS_DOKTER)){
                if($LJ->STATUS_DOKTER == "4"){
                    $STATUS = '<h5 class="text-start">Terlambat ('.$LJ->NOTE_DISPLAY.')</h5>';
                    $KAMAR = $LJ->KAMAR;
                }elseif($LJ->STATUS_DOKTER == "5"){
                    $STATUS = '<h5 class="text-start">Batal ('.$LJ->NOTE_DISPLAY.')</h5>';
                    $KAMAR = $LJ->KAMAR;
                }elseif($LJ->STATUS_DOKTER =="6"){
                    $STATUS = '<h5 class="text-start">Selesai</h5>';
                    $KAMAR = $LJ->KAMAR;
                }
            }elseif($LJ->AWAL <= $jamSekarang && $LJ->AKHIR >= $jamSekarang && empty($LJ->ANTRIAN_DILAYANI)){
                $STATUS = '<h5 class="text-start">Terlambat</h5>';
                $KAMAR = $LJ->KAMAR;
            }elseif($LJ->AWAL > $jamSekarang){
                $STATUS = '<h5 class="text-start">Terjadwal</h5>';
                $KAMAR = $LJ->KAMAR;
            }
                
            $data[] = array(
                $no,
                '<h4 class="jadwalw">'. sprintf('%02d:00', $LJ->AWAL) . " - " . sprintf('%02d:00', $LJ->AKHIR) . '</h4>',
                '<h5 class="text-start">' . $LJ->DOKTER . '</h5>',
                $LJ->RUANG_LAYANAN,
                '<h4 class="jadwalw">' . $KAMAR . '</h4>',
                $STATUS,
            );
            $no++;
        }
    
        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listJadwal->num_rows($RUANGAN),
            "recordsFiltered" => $listJadwal->num_rows($RUANGAN),
            "data"            => $data
        );
        echo json_encode($output);
    }

    public function get_data_jadwal_dokter_c(){
		$draw   = intval($this->input->POST("draw"));
		$start  = intval($this->input->POST("start"));
		$length = intval($this->input->POST("length"));
	
        $JENISBANG = 2;
        $RUANGAN = null;
		$listJadwal = $this->Dashboard_modeln->datatablesJadwal($JENISBANG, $RUANGAN);
	
		$data = array();
		$no = 1;
        foreach ($listJadwal->result() as $LJ) {

            $jamSekarang = date("G");
            $STATUS = '';
            $KAMAR = $LJ->KAMAR;

            if($LJ->AKHIR <= $jamSekarang){
                $STATUS = 'Selesai';
                $KAMAR = '';
            }elseif(!empty($LJ->ANTRIAN_DILAYANI && empty($LJ->STATUS_DOKTER) && $LJ->AWAL <= $jamSekarang && $LJ->AKHIR >= $jamSekarang)){
                if($LJ->ANTRIAN_DILAYANI == "ASN"){
                    $STATUS = '<h5 style="color:#02B891"> Sedang Melayani Pasien</h5>';
                }else{
                    $STATUS = '<h5 style="color:#02B891"> Sedang Melayani Pasien No.Urut ('.$LJ->ANTRIAN_DILAYANI.')</h5>';
                }
                if(empty($LJ->STATUS_DOKTER) && !empty($LJ->KAMAR)){
                    $KAMAR = $LJ->KAMAR;
                }else{
                    $KAMAR ='';
                }
            }elseif(!empty($LJ->STATUS_DOKTER)){
                if($LJ->STATUS_DOKTER == "4"){
                    $STATUS = '<h5 class="text-start">Terlambat ('.$LJ->NOTE_DISPLAY.')</h5>';
                    $KAMAR = $LJ->KAMAR;
                }elseif($LJ->STATUS_DOKTER == "5"){
                    $STATUS = '<h5 class="text-start">Batal ('.$LJ->NOTE_DISPLAY.')</h5>';
                    $KAMAR = $LJ->KAMAR;
                }elseif($LJ->STATUS_DOKTER =="6"){
                    $STATUS = '<h5 class="text-start">Selesai</h5>';
                    $KAMAR = $LJ->KAMAR;
                }
            }elseif($LJ->AWAL <= $jamSekarang && $LJ->AKHIR >= $jamSekarang && empty($LJ->ANTRIAN_DILAYANI)){
                $STATUS = '<h5 class="text-start">Terlambat</h5>';
                $KAMAR = $LJ->KAMAR;
            }elseif($LJ->AWAL > $jamSekarang){
                $STATUS = '<h5 class="text-start">Terjadwal</h5>';
                $KAMAR = $LJ->KAMAR;
            }
                
            $data[] = array(
                $no,
                '<h4 class="jadwalw">'. sprintf('%02d:00', $LJ->AWAL) . " - " . sprintf('%02d:00', $LJ->AKHIR) . '</h4>',
                '<h5 class="text-start">' . $LJ->DOKTER . '</h5>',
                $LJ->RUANG_LAYANAN,
                '<h4 class="jadwalw">' . $KAMAR . '</h4>',
                $STATUS,
            );
            $no++;
        }
	
		$output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listJadwal->num_rows(),
            "recordsFiltered" => $listJadwal->num_rows(),
            "data"            => $data
		);
		echo json_encode($output);
	}


    public function get_data_jadwal_dokter_cs(){
        $draw   = intval($this->input->POST("draw"));
        $start  = intval($this->input->POST("start"));
        $length = intval($this->input->POST("length"));
    
        $JENISBANG = 2;
        $RUANGAN = $this->input->post('RUANGAN');
        $listJadwal = $this->Dashboard_modeln->datatablesJadwal($JENISBANG, $RUANGAN);
    
        $data = array();
		$no = 1;
        foreach ($listJadwal->result() as $LJ) {

            $jamSekarang = date("G");
            $STATUS = '';
            $KAMAR = $LJ->KAMAR;

            if($LJ->AKHIR <= $jamSekarang){
                $STATUS = 'Selesai';
                $KAMAR = '';
            }elseif(!empty($LJ->ANTRIAN_DILAYANI && empty($LJ->STATUS_DOKTER) && $LJ->AWAL <= $jamSekarang && $LJ->AKHIR >= $jamSekarang)){
                if($LJ->ANTRIAN_DILAYANI == "ASN"){
                    $STATUS = '<h5 style="color:#02B891"> Sedang Melayani Pasien</h5>';
                }else{
                    $STATUS = '<h5 style="color:#02B891"> Sedang Melayani Pasien No.Urut ('.$LJ->ANTRIAN_DILAYANI.')</h5>';
                }
                if(empty($LJ->STATUS_DOKTER) && !empty($LJ->KAMAR)){
                    $KAMAR = $LJ->KAMAR;
                }else{
                    $KAMAR ='';
                }
            }elseif(!empty($LJ->STATUS_DOKTER)){
                if($LJ->STATUS_DOKTER == "4"){
                    $STATUS = '<h5 class="text-start">Terlambat ('.$LJ->NOTE_DISPLAY.')</h5>';
                    $KAMAR = $LJ->KAMAR;
                }elseif($LJ->STATUS_DOKTER == "5"){
                    $STATUS = '<h5 class="text-start">Batal ('.$LJ->NOTE_DISPLAY.')</h5>';
                    $KAMAR = $LJ->KAMAR;
                }elseif($LJ->STATUS_DOKTER =="6"){
                    $STATUS = '<h5 class="text-start">Selesai</h5>';
                    $KAMAR = $LJ->KAMAR;
                }
            }elseif($LJ->AWAL <= $jamSekarang && $LJ->AKHIR >= $jamSekarang && empty($LJ->ANTRIAN_DILAYANI)){
                $STATUS = '<h5 class="text-start">Terlambat</h5>';
                $KAMAR = $LJ->KAMAR;
            }elseif($LJ->AWAL > $jamSekarang){
                $STATUS = '<h5 class="text-start">Terjadwal</h5>';
                $KAMAR = $LJ->KAMAR;
            }
                
            $data[] = array(
                $no,
                '<h4 class="jadwalw">'. sprintf('%02d:00', $LJ->AWAL) . " - " . sprintf('%02d:00', $LJ->AKHIR) . '</h4>',
                '<h5 class="text-start">' . $LJ->DOKTER . '</h5>',
                $LJ->RUANG_LAYANAN,
                '<h4 class="jadwalw">' . $KAMAR . '</h4>',
                $STATUS,
            );
            $no++;
        }
    
        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listJadwal->num_rows($RUANGAN),
            "recordsFiltered" => $listJadwal->num_rows($RUANGAN),
            "data"            => $data
        );
        echo json_encode($output);
    }
}
