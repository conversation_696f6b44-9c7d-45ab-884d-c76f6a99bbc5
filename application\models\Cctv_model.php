<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cctv_model extends CI_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    public function get_data_detail($start, $length, $search, $order, $channel_id, $start_date = null, $end_date = null)
    {
        $this->db->select('*');
        $this->db->from('db_cctv_ok.data_detail');
        $this->db->where('channel_id', $channel_id);
        $this->db->where('similarity >', 70);

        // Filter berdasarkan tanggal jika ada
        if (!empty($start_date) && !empty($end_date)) {
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) >=', $start_date);
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) <=', $end_date);
        }

        if (!empty($search)) {
            $this->db->group_start(); // Start grouping
            $this->db->like('channel_name', $search);
            $this->db->or_like('name', $search);
            $this->db->or_like('capture_time', $search);
            $this->db->group_end(); // End grouping
        }

        if (!empty($order)) {
            $this->db->order_by($order['column'], $order['dir']);
        }

        $this->db->limit($length, $start);
        $query = $this->db->get();
        return $query->result_array();
    }

    public function count_all()
    {
        return $this->db->count_all('db_cctv_ok.data_detail');
    }

    public function count_filtered($search, $channel_id, $start_date = null, $end_date = null)
    {
        $this->db->select('*');
        $this->db->from('db_cctv_ok.data_detail');
        $this->db->where('channel_id', $channel_id);
        $this->db->where('similarity >', 70);

        // Filter berdasarkan tanggal jika ada
        if (!empty($start_date) && !empty($end_date)) {
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) >=', $start_date);
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) <=', $end_date);
        }

        if (!empty($search)) {
            $this->db->group_start(); // Start grouping
            $this->db->like('channel_name', $search);
            $this->db->or_like('name', $search);
            $this->db->or_like('capture_time', $search);
            $this->db->group_end(); // End grouping
        }

        return $this->db->count_all_results();
    }

    // Method untuk Ruang Operasi - semua data kecuali channel 4
    public function get_data_ruang_operasi($start, $length, $search, $order, $start_date = null, $end_date = null, $channel_id = null)
    {
        $this->db->select('*');
        $this->db->from('db_cctv_ok.data_detail');
        $this->db->where('channel_id !=', 4);
        $this->db->where('similarity >', 70);

        // Filter berdasarkan channel jika ada
        if (!empty($channel_id)) {
            if (strpos($channel_id, '/') !== false) {
                // Jika channel_id berisi multiple ID yang dipisahkan dengan "/"
                $channel_ids = explode('/', $channel_id);
                $this->db->where_in('channel_id', $channel_ids);
            } else {
                $this->db->where('channel_id', $channel_id);
            }
        }

        // Filter berdasarkan tanggal jika ada
        if (!empty($start_date) && !empty($end_date)) {
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) >=', $start_date);
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) <=', $end_date);
        }

        if (!empty($search)) {
            $this->db->group_start(); // Start grouping
            $this->db->like('channel_name', $search);
            $this->db->or_like('name', $search);
            $this->db->or_like('capture_time', $search);
            $this->db->group_end(); // End grouping
        }

        if (!empty($order)) {
            $this->db->order_by($order['column'], $order['dir']);
        }

        $this->db->limit($length, $start);
        $query = $this->db->get();
        return $query->result_array();
    }

    public function count_all_ruang_operasi()
    {
        $this->db->where('channel_id !=', 4);
        return $this->db->count_all_results('db_cctv_ok.data_detail');
    }

    public function count_filtered_ruang_operasi($search, $start_date = null, $end_date = null, $channel_id = null)
    {
        $this->db->select('*');
        $this->db->from('db_cctv_ok.data_detail');
        $this->db->where('channel_id !=', 4);
        $this->db->where('similarity >', 70);

        // Filter berdasarkan channel jika ada
        if (!empty($channel_id)) {
            if (strpos($channel_id, '/') !== false) {
                // Jika channel_id berisi multiple ID yang dipisahkan dengan "/"
                $channel_ids = explode('/', $channel_id);
                $this->db->where_in('channel_id', $channel_ids);
            } else {
                $this->db->where('channel_id', $channel_id);
            }
        }

        // Filter berdasarkan tanggal jika ada
        if (!empty($start_date) && !empty($end_date)) {
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) >=', $start_date);
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) <=', $end_date);
        }

        if (!empty($search)) {
            $this->db->group_start(); // Start grouping
            $this->db->like('channel_name', $search);
            $this->db->or_like('name', $search);
            $this->db->or_like('capture_time', $search);
            $this->db->group_end(); // End grouping
        }

        return $this->db->count_all_results();
    }

    // Method untuk mendapatkan daftar channel (kecuali channel 4)
    public function get_channels_ruang_operasi()
    {
        // Ambil semua channel terlebih dahulu
        $this->db->distinct();
        $this->db->select('channel_id, channel_name');
        $this->db->from('db_cctv_ok.data_detail');
        $this->db->where('channel_id !=', 4);
        $this->db->order_by('channel_name', 'ASC');
        $query = $this->db->get();
        $channels = $query->result_array();
        
        // Kelompokkan channel berdasarkan pola nama yang mirip
        $grouped = array();
        foreach ($channels as $channel) {
            $name = strtolower(trim($channel['channel_name']));
            // Ekstrak pola dasar (contoh: "ok 1", "ok 2", "ok 5.6", dll)
            if (preg_match('/^(ok\s*\d+(?:\.\d+)?)/', $name, $matches)) {
                $base_name = $matches[1];
                if (!isset($grouped[$base_name])) {
                    $grouped[$base_name] = array(
                        'channel_ids' => array(),
                        'display_name' => ''
                    );
                }
                $grouped[$base_name]['channel_ids'][] = $channel['channel_id'];
                
                // Tentukan nama display yang lebih baik
                if (empty($grouped[$base_name]['display_name'])) {
                    $grouped[$base_name]['display_name'] = ucwords($base_name);
                }
            } else {
                // Jika tidak cocok pola, masukkan sebagai channel terpisah
                $grouped[$name] = array(
                    'channel_ids' => array($channel['channel_id']),
                    'display_name' => $channel['channel_name']
                );
            }
        }
        
        // Format hasil untuk dropdown
        $result = array();
        foreach ($grouped as $group) {
            $result[] = array(
                'channel_id' => implode('/', $group['channel_ids']),
                'channel_name' => $group['display_name']
            );
        }
        
        return $result;
    }
}
