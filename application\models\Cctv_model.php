<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cctv_model extends CI_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    public function get_data_detail($start, $length, $search, $order, $channel_id, $start_date = null, $end_date = null)
    {
        $this->db->select('*');
        $this->db->from('db_cctv_ok.data_detail');
        $this->db->where('channel_id', $channel_id);
        $this->db->where('similarity >', 70);

        // Filter berdasarkan tanggal jika ada
        if (!empty($start_date) && !empty($end_date)) {
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) >=', $start_date);
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) <=', $end_date);
        }

        if (!empty($search)) {
            $this->db->group_start(); // Start grouping
            $this->db->like('channel_name', $search);
            $this->db->or_like('name', $search);
            $this->db->or_like('capture_time', $search);
            $this->db->group_end(); // End grouping
        }

        if (!empty($order)) {
            $this->db->order_by($order['column'], $order['dir']);
        }

        $this->db->limit($length, $start);
        $query = $this->db->get();
        return $query->result_array();
    }

    public function count_all()
    {
        return $this->db->count_all('db_cctv_ok.data_detail');
    }

    public function count_filtered($search, $channel_id, $start_date = null, $end_date = null)
    {
        $this->db->select('*');
        $this->db->from('db_cctv_ok.data_detail');
        $this->db->where('channel_id', $channel_id);
        $this->db->where('similarity >', 70);

        // Filter berdasarkan tanggal jika ada
        if (!empty($start_date) && !empty($end_date)) {
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) >=', $start_date);
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) <=', $end_date);
        }

        if (!empty($search)) {
            $this->db->group_start(); // Start grouping
            $this->db->like('channel_name', $search);
            $this->db->or_like('name', $search);
            $this->db->or_like('capture_time', $search);
            $this->db->group_end(); // End grouping
        }

        return $this->db->count_all_results();
    }

    // Method untuk Ruang Operasi - semua data kecuali channel 4
    public function get_data_ruang_operasi($start, $length, $search, $order, $start_date = null, $end_date = null, $channel_id = null)
    {
        $this->db->select('*');
        $this->db->from('db_cctv_ok.data_detail');
        $this->db->where('similarity >', 70);

        // Filter berdasarkan channel jika ada
        if (!empty($channel_id)) {
            if (strpos($channel_id, '/') !== false) {
                // Jika channel_id berisi multiple ID yang dipisahkan dengan "/"
                $channel_ids = explode('/', $channel_id);
                // Pastikan hanya channel yang dipilih yang ditampilkan
                $this->db->where_in('channel_id', $channel_ids);
            } else {
                // Filter hanya channel yang dipilih
                $this->db->where('channel_id', $channel_id);
            }
        } else {
            // Jika tidak ada channel yang dipilih, tampilkan semua kecuali channel 4
            $this->db->where('channel_id !=', 4);
        }

        // Filter berdasarkan tanggal jika ada
        if (!empty($start_date) && !empty($end_date)) {
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) >=', $start_date);
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) <=', $end_date);
        }

        if (!empty($search)) {
            $this->db->group_start(); // Start grouping
            $this->db->like('channel_name', $search);
            $this->db->or_like('name', $search);
            $this->db->or_like('capture_time', $search);
            $this->db->group_end(); // End grouping
        }

        if (!empty($order)) {
            $this->db->order_by($order['column'], $order['dir']);
        }

        $this->db->limit($length, $start);
        $query = $this->db->get();
        return $query->result_array();
    }

    public function count_all_ruang_operasi()
    {
        $this->db->where('channel_id !=', 4);
        return $this->db->count_all_results('db_cctv_ok.data_detail');
    }

    public function count_filtered_ruang_operasi($search, $start_date = null, $end_date = null, $channel_id = null)
    {
        $this->db->select('*');
        $this->db->from('db_cctv_ok.data_detail');
        $this->db->where('similarity >', 70);

        // Filter berdasarkan channel jika ada
        if (!empty($channel_id)) {
            if (strpos($channel_id, '/') !== false) {
                // Jika channel_id berisi multiple ID yang dipisahkan dengan "/"
                $channel_ids = explode('/', $channel_id);
                // Pastikan hanya channel yang dipilih yang dihitung
                $this->db->where_in('channel_id', $channel_ids);
            } else {
                // Filter hanya channel yang dipilih
                $this->db->where('channel_id', $channel_id);
            }
        } else {
            // Jika tidak ada channel yang dipilih, hitung semua kecuali channel 4
            $this->db->where('channel_id !=', 4);
        }

        // Filter berdasarkan tanggal jika ada
        if (!empty($start_date) && !empty($end_date)) {
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) >=', $start_date);
            $this->db->where('DATE(FROM_UNIXTIME(capture_time)) <=', $end_date);
        }

        if (!empty($search)) {
            $this->db->group_start(); // Start grouping
            $this->db->like('channel_name', $search);
            $this->db->or_like('name', $search);
            $this->db->or_like('capture_time', $search);
            $this->db->group_end(); // End grouping
        }

        return $this->db->count_all_results();
    }

    // Method untuk mendapatkan daftar channel (kecuali channel 4)
    public function get_channels_ruang_operasi()
    {
        // Ambil semua channel terlebih dahulu tanpa pengelompokan untuk menghindari kebingungan filtering
        $this->db->distinct();
        $this->db->select('channel_id, channel_name');
        $this->db->from('db_cctv_ok.data_detail');
        $this->db->where('channel_id !=', 4);
        $this->db->order_by('channel_id', 'ASC');
        $query = $this->db->get();
        $channels = $query->result_array();

        // Return channel individual tanpa pengelompokan untuk filtering yang lebih akurat
        return $channels;
    }

    // Method untuk debugging - mendapatkan query yang dijalankan
    public function get_last_query()
    {
        return $this->db->last_query();
    }

    // Method untuk testing filtering - mengembalikan data dengan informasi debug
    public function test_channel_filter($channel_id = null)
    {
        $this->db->select('channel_id, channel_name, COUNT(*) as total_records');
        $this->db->from('db_cctv_ok.data_detail');
        $this->db->where('similarity >', 70);

        if (!empty($channel_id)) {
            if (strpos($channel_id, '/') !== false) {
                $channel_ids = explode('/', $channel_id);
                $this->db->where_in('channel_id', $channel_ids);
            } else {
                $this->db->where('channel_id', $channel_id);
            }
        } else {
            $this->db->where('channel_id !=', 4);
        }

        $this->db->group_by('channel_id, channel_name');
        $this->db->order_by('channel_id', 'ASC');

        $query = $this->db->get();
        return array(
            'data' => $query->result_array(),
            'query' => $this->db->last_query()
        );
    }
}
