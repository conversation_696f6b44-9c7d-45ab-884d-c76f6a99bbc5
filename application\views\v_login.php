<?php
function sanitize_output($buffer)
{
    $search = array(
        '/\>[^\S ]+/s',     // strip whitespaces after tags, except space
        '/[^\S ]+\</s',     // strip whitespaces before tags, except space
        '/(\s)+/s',         // shorten multiple whitespace sequences
        '/<!--(.|\s)*?-->/' // Remove HTML comments
    );
    $replace = array('>', '<', '\\1', '');
    $buffer = preg_replace($search, $replace, $buffer);
    return $buffer;
}
ob_start("sanitize_output");
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Responsive Admin Dashboard Template">
    <meta name="keywords" content="admin,dashboard">
    <meta name="author" content="stacks">
    <!-- The above 6 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <!-- favicon -->
    <link rel="icon" href="<?php echo base_url('assets/images/dharmais_icon.png'); ?>" type="image/png">

    <!-- Title -->
    <title>Dashboard RSKD</title>

    <!-- Styles -->
    <link href="https://fonts.googleapis.com/css?family=Poppins:400,500,700,800&display=swap" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/bootstrap/css/bootstrap.min.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/font-awesome/css/all.min.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/perfectscroll/perfect-scrollbar.css') ?>" rel="stylesheet">

    <!-- Theme Styles -->
    <link href="<?= base_url('assets/css/main.min.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/css/custom.css') ?>" rel="stylesheet">
</head>

<body class="login-page">
    <div class="container">
        <div class="row justify-content-md-center">
            <div class="col-md-12 col-lg-4">
                <div class="card login-box-container">
                    <div class="card-body">
                        <div class="authent-logo">
                            <img src="<?= base_url('assets/images/dharmais.png') ?>" alt="" width="100">
                        </div>
                        <div class="authent-text">
                            <h1>Dashboard CCTV</h1>
                        </div>
                        <form action="#" id="formLogin">
                            <div class="mb-3">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="username" name="username" placeholder="Username">
                                    <label for="username">Username</label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-floating">
                                    <input type="password" class="form-control" name="password" id="password" placeholder="Password">
                                    <label for="password">Password</label>
                                </div>
                            </div>
                        </form>
                        <div class="d-grid">
                            <button type="submit" id="btnLogin" class="btn btn-login btn-block btn-success btnLogin">LOGIN</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Javascripts -->
    <script src="<?= base_url('assets/plugins/jquery/jquery-3.4.1.min.js') ?>"></script>
    <script src="https://unpkg.com/@popperjs/core@2"></script>
    <script src="<?= base_url('assets/plugins/bootstrap/js/bootstrap.min.js') ?>"></script>
    <script src="https://unpkg.com/feather-icons"></script>
    <script src="<?= base_url('assets/plugins/perfectscroll/perfect-scrollbar.min.js') ?>"></script>
    <script src="<?= base_url('assets/plugins/sweetalert2/sweetalert2.all.min.js') ?>"></script>
    <script src="<?= base_url('assets/js/main.min.js') ?>"></script>

    <script>
        $('#username').keydown(function(e) {
            if (e.which == 13) {
                $('#password').focus();
            }
        });

        $('#password').keydown(function(e) {
            if (e.which == 13) {
                $('#btnLogin').click();
            }
        });

        $("#btnLogin").unbind().click(function() {
            var user = document.getElementById("username").value;
            var form = $("#formLogin").serialize();
            $.ajax("<?= base_url('start') ?>", {
                dataType: 'json',
                type: 'POST',
                data: form,
                success: function(data) {
                    if (data.status == 200) {
                        Swal.fire({
                            type: 'success',
                            title: 'Login Berhasil!',
                            text: 'Anda akan di arahkan dalam beberapa Detik',
                            timer: 3000,
                            showCancelButton: false,
                            showConfirmButton: false
                        });
                        var link = data.data.link;
                        setTimeout(function() {
                            window.location.replace(link);
                        }, 3000);
                    } else {
                        Swal.fire({
                            type: 'error',
                            title: 'Notifikasi',
                            text: data.message,
                            timer: 3000,
                            showCancelButton: false,
                            showConfirmButton: false
                        }).then(function() {
                            $('[name="username"]').val("");
                            $('[name="password"]').val("");
                        });
                    }
                }
            });
        });
    </script>
</body>

</html>
<?php ob_end_flush(); ?>